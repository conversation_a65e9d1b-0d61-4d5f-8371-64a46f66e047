<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .admin-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.12);
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .form-label {
        font-weight: 600;
        color: #800000;
    }

    .form-control:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    .form-select:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    .btn-success {
        background-color: #28a745;
        border-color: #28a745;
    }

    .btn-success:hover {
        background-color: #218838;
        border-color: #1e7e34;
    }

    .workplan-info {
        background: linear-gradient(135deg, #800000 0%, #28a745 100%);
        color: white;
        border-radius: 10px;
        padding: 1rem;
    }

    .activity-info {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        border-left: 4px solid #28a745;
    }

    .resource-section {
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1rem;
    }

    .resource-section h6 {
        color: #800000;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 0.5rem;
        margin-bottom: 1rem;
    }

    .checkbox-list {
        max-height: 200px;
        overflow-y: auto;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 0.5rem;
    }

    .form-check {
        margin-bottom: 0.5rem;
    }

    .selected-count {
        background: #28a745;
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.75rem;
        margin-left: 0.5rem;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Workplan Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="workplan-info">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5 class="mb-1">
                            <i class="fas fa-clipboard-list me-2"></i><?= esc($workplan['position_name']) ?> - Fiscal Year <?= esc($workplan['fiscal_year']) ?>
                        </h5>
                        <p class="mb-0 opacity-75">
                            Supervisor: <?= esc($workplan['supervisor_name']) ?> | 
                            Current Activities: <?= $workplan['activities_count'] ?>
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities') ?>" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left me-2"></i>Back to Activities
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="activity-info">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h6 class="mb-1">
                            <i class="fas fa-tasks me-2"></i><?= esc($activity['name']) ?>
                        </h6>
                        <p class="mb-0 text-muted">
                            Type: <?= esc($activity['type']) ?> | 
                            Status: <?= esc($activity['status']) ?> | 
                            Duration: <?= date('M d', strtotime($activity['start_date'])) ?> - <?= date('M d, Y', strtotime($activity['end_date'])) ?>
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <span class="badge bg-info">Currently Linked: <?= count($current_links['plans']) + count($current_links['budget_codes']) + count($current_links['assets']) + count($current_links['officers']) ?> resources</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-link me-2"></i>Link Activity Resources
                        </h3>
                        <p class="text-muted mb-0">
                            Link this activity to organizational plans, budget codes, assets, and officers.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Activities
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Linking Form -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <form action="<?= site_url('workplan-management/' . $workplan['id'] . '/activities/' . $activity['id'] . '/link') ?>" method="POST">
                    <?= csrf_field() ?>
                    
                    <div class="row g-4">
                        <!-- Plans Section -->
                        <div class="col-md-6">
                            <div class="resource-section">
                                <h6>
                                    <i class="fas fa-clipboard-list me-2"></i>Organizational Plans
                                    <span class="selected-count" id="plans-count">0 selected</span>
                                </h6>
                                <p class="text-muted small mb-3">Link this activity to relevant organizational plans (Corporate or Development plans).</p>
                                
                                <div class="checkbox-list">
                                    <?php foreach ($plans as $plan): ?>
                                        <div class="form-check">
                                            <input class="form-check-input plan-checkbox" type="checkbox" 
                                                   name="plans[]" value="<?= $plan['id'] ?>" 
                                                   id="plan_<?= $plan['id'] ?>"
                                                   <?= in_array($plan['id'], $current_links['plans']) ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="plan_<?= $plan['id'] ?>">
                                                <strong><?= esc($plan['name']) ?></strong>
                                                <br><small class="text-muted"><?= esc($plan['type']) ?> Plan</small>
                                            </label>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Budget Codes Section -->
                        <div class="col-md-6">
                            <div class="resource-section">
                                <h6>
                                    <i class="fas fa-dollar-sign me-2"></i>Budget Codes
                                    <span class="selected-count" id="budget-count">0 selected</span>
                                </h6>
                                <p class="text-muted small mb-3">Link this activity to relevant budget codes for financial tracking.</p>
                                
                                <div class="checkbox-list">
                                    <?php foreach ($budget_codes as $budget): ?>
                                        <div class="form-check">
                                            <input class="form-check-input budget-checkbox" type="checkbox" 
                                                   name="budget_codes[]" value="<?= $budget['id'] ?>" 
                                                   id="budget_<?= $budget['id'] ?>"
                                                   <?= in_array($budget['id'], $current_links['budget_codes']) ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="budget_<?= $budget['id'] ?>">
                                                <strong><?= esc($budget['code']) ?></strong> - <?= esc($budget['name']) ?>
                                                <br><small class="text-muted"><?= esc($budget['type']) ?></small>
                                            </label>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Assets Section -->
                        <div class="col-md-6">
                            <div class="resource-section">
                                <h6>
                                    <i class="fas fa-box me-2"></i>Assets & Resources
                                    <span class="selected-count" id="assets-count">0 selected</span>
                                </h6>
                                <p class="text-muted small mb-3">Select assets and resources needed for this activity.</p>
                                
                                <div class="checkbox-list">
                                    <?php foreach ($assets as $asset): ?>
                                        <div class="form-check">
                                            <input class="form-check-input asset-checkbox" type="checkbox" 
                                                   name="assets[]" value="<?= $asset['id'] ?>" 
                                                   id="asset_<?= $asset['id'] ?>"
                                                   <?= in_array($asset['id'], $current_links['assets']) ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="asset_<?= $asset['id'] ?>">
                                                <strong><?= esc($asset['name']) ?></strong>
                                                <br><small class="text-muted"><?= esc($asset['type']) ?></small>
                                            </label>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Officers Section -->
                        <div class="col-md-6">
                            <div class="resource-section">
                                <h6>
                                    <i class="fas fa-users me-2"></i>Officers & Staff
                                    <span class="selected-count" id="officers-count">0 selected</span>
                                </h6>
                                <p class="text-muted small mb-3">Assign officers and staff members to this activity.</p>
                                
                                <div class="checkbox-list">
                                    <?php foreach ($officers as $officer): ?>
                                        <div class="form-check">
                                            <input class="form-check-input officer-checkbox" type="checkbox" 
                                                   name="officers[]" value="<?= $officer['id'] ?>" 
                                                   id="officer_<?= $officer['id'] ?>"
                                                   <?= in_array($officer['id'], $current_links['officers']) ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="officer_<?= $officer['id'] ?>">
                                                <strong><?= esc($officer['name']) ?></strong>
                                                <br><small class="text-muted"><?= esc($officer['position']) ?></small>
                                            </label>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Notes -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="resource-section">
                                <h6>
                                    <i class="fas fa-sticky-note me-2"></i>Linking Notes
                                </h6>
                                <textarea class="form-control" name="linking_notes" rows="3" 
                                          placeholder="Enter any notes about the resource linking...">Resources linked for <?= esc($activity['name']) ?> on <?= date('M d, Y') ?>. This activity requires coordination between selected officers and proper utilization of assigned assets and budget allocations.</textarea>
                                <div class="form-text">Optional notes about the resource linking for this activity.</div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-link me-2"></i>Save Resource Links
                                </button>
                                <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities') ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                                <button type="button" class="btn btn-outline-warning" onclick="clearAllSelections()">
                                    <i class="fas fa-eraser me-2"></i>Clear All
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Summary Card -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <h6 class="fw-bold text-info mb-3">
                    <i class="fas fa-info-circle me-2"></i>Resource Linking Summary
                </h6>
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <i class="fas fa-clipboard-list fs-2 mb-2 text-primary"></i>
                            <h5 class="fw-bold" id="summary-plans">0</h5>
                            <small class="text-muted">Plans Linked</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <i class="fas fa-dollar-sign fs-2 mb-2 text-success"></i>
                            <h5 class="fw-bold" id="summary-budget">0</h5>
                            <small class="text-muted">Budget Codes</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <i class="fas fa-box fs-2 mb-2 text-warning"></i>
                            <h5 class="fw-bold" id="summary-assets">0</h5>
                            <small class="text-muted">Assets</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <i class="fas fa-users fs-2 mb-2 text-info"></i>
                            <h5 class="fw-bold" id="summary-officers">0</h5>
                            <small class="text-muted">Officers</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Update counters
        function updateCounters() {
            const plansCount = document.querySelectorAll('.plan-checkbox:checked').length;
            const budgetCount = document.querySelectorAll('.budget-checkbox:checked').length;
            const assetsCount = document.querySelectorAll('.asset-checkbox:checked').length;
            const officersCount = document.querySelectorAll('.officer-checkbox:checked').length;

            document.getElementById('plans-count').textContent = plansCount + ' selected';
            document.getElementById('budget-count').textContent = budgetCount + ' selected';
            document.getElementById('assets-count').textContent = assetsCount + ' selected';
            document.getElementById('officers-count').textContent = officersCount + ' selected';

            document.getElementById('summary-plans').textContent = plansCount;
            document.getElementById('summary-budget').textContent = budgetCount;
            document.getElementById('summary-assets').textContent = assetsCount;
            document.getElementById('summary-officers').textContent = officersCount;
        }

        // Add event listeners to all checkboxes
        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', updateCounters);
        });

        // Clear all selections
        window.clearAllSelections = function() {
            if (confirm('Are you sure you want to clear all resource selections?')) {
                document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
                    checkbox.checked = false;
                });
                updateCounters();
            }
        };

        // Form submission
        document.querySelector('form').addEventListener('submit', function(e) {
            const totalSelected = document.querySelectorAll('input[type="checkbox"]:checked').length;
            
            if (totalSelected === 0) {
                if (!confirm('No resources are selected. Do you want to continue without linking any resources?')) {
                    e.preventDefault();
                    return false;
                }
            }

            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving Links...';
            submitBtn.disabled = true;

            // Re-enable button after 3 seconds (in case of validation errors)
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 3000);
        });

        // Initialize counters
        updateCounters();

        console.log('Activity Resource Linking initialized successfully');
    });
</script>
<?= $this->endSection() ?>
