<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .admin-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.12);
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .form-label {
        font-weight: 600;
        color: #800000;
    }

    .form-control:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    .form-select:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    .btn-success {
        background-color: #28a745;
        border-color: #28a745;
    }

    .btn-success:hover {
        background-color: #218838;
        border-color: #1e7e34;
    }

    .required {
        color: #dc3545;
    }

    .workplan-info {
        background: linear-gradient(135deg, #800000 0%, #28a745 100%);
        color: white;
        border-radius: 10px;
        padding: 1rem;
    }

    .activity-info {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        border-left: 4px solid #28a745;
    }

    .info-badge {
        background-color: #e9ecef;
        color: #495057;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-size: 0.875rem;
    }

    .progress-bar-custom {
        height: 8px;
        border-radius: 4px;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Workplan Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="workplan-info">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5 class="mb-1">
                            <i class="fas fa-clipboard-list me-2"></i><?= esc($workplan['position_name']) ?> - Fiscal Year <?= esc($workplan['fiscal_year']) ?>
                        </h5>
                        <p class="mb-0 opacity-75">
                            Supervisor: <?= esc($workplan['supervisor_name']) ?> | 
                            Current Activities: <?= $workplan['activities_count'] ?>
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities/' . $activity['id'] . '/targets') ?>" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left me-2"></i>Back to Targets
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="activity-info">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h6 class="mb-1">
                            <i class="fas fa-tasks me-2"></i>Activity: <?= esc($activity['name']) ?>
                        </h6>
                        <p class="mb-0 text-muted">
                            Type: <?= esc($activity['type']) ?> | 
                            Status: <?= esc($activity['status']) ?> | 
                            Duration: <?= date('M d', strtotime($activity['start_date'])) ?> - <?= date('M d, Y', strtotime($activity['end_date'])) ?>
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities/' . $activity['id'] . '/targets') ?>" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-arrow-left me-2"></i>Back to Targets
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-edit me-2"></i>Edit Target
                        </h3>
                        <p class="text-muted mb-0">
                            Update target information: <?= esc($target['name']) ?>
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities/' . $activity['id'] . '/targets') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Targets
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <h6 class="fw-bold text-info mb-3">
                    <i class="fas fa-info-circle me-2"></i>Current Target Information
                </h6>
                <div class="row g-3">
                    <div class="col-md-3">
                        <div class="info-badge">
                            <strong>Type:</strong><br>
                            <?= esc($target['type']) ?>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-badge">
                            <strong>Target Value:</strong><br>
                            <?= esc($target['target_value']) ?> <?= esc($target['unit']) ?>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-badge">
                            <strong>Current Value:</strong><br>
                            <?= esc($target['current_value']) ?> <?= esc($target['unit']) ?>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-badge">
                            <strong>Progress:</strong><br>
                            <div class="progress progress-bar-custom mt-1">
                                <div class="progress-bar bg-success" style="width: <?= $target['completion_percentage'] ?>%"></div>
                            </div>
                            <small><?= $target['completion_percentage'] ?>%</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <h5 class="fw-bold mb-4" style="color: #800000;">
                    <i class="fas fa-bullseye me-2"></i>Update Target Information
                </h5>

                <form action="<?= site_url('workplan-management/' . $workplan['id'] . '/activities/' . $activity['id'] . '/targets/' . $target['id']) ?>" method="POST">
                    <?= csrf_field() ?>
                    <input type="hidden" name="_method" value="PUT">
                    
                    <div class="row g-4">
                        <!-- Target Name -->
                        <div class="col-md-8">
                            <label for="name" class="form-label">
                                Target Name <span class="required">*</span>
                            </label>
                            <input type="text" class="form-control" id="name" name="name" required
                                   value="<?= esc($target['name']) ?>"
                                   placeholder="Enter target name...">
                            <div class="form-text">Provide a clear and specific name for this target.</div>
                        </div>

                        <!-- Target Type -->
                        <div class="col-md-4">
                            <label for="type" class="form-label">
                                Target Type <span class="required">*</span>
                            </label>
                            <select class="form-select" id="type" name="type" required>
                                <option value="">Select Type</option>
                                <?php foreach ($target_types as $key => $value): ?>
                                    <option value="<?= $key ?>" <?= $key === $target['type'] ? 'selected' : '' ?>>
                                        <?= esc($value) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Select the type of target.</div>
                        </div>

                        <!-- Target Value -->
                        <div class="col-md-4">
                            <label for="target_value" class="form-label">
                                Target Value <span class="required">*</span>
                            </label>
                            <input type="number" class="form-control" id="target_value" name="target_value" required
                                   min="0" step="0.01" value="<?= esc($target['target_value']) ?>"
                                   placeholder="0">
                            <div class="form-text">The target value to achieve.</div>
                        </div>

                        <!-- Current Value -->
                        <div class="col-md-4">
                            <label for="current_value" class="form-label">
                                Current Value
                            </label>
                            <input type="number" class="form-control" id="current_value" name="current_value"
                                   min="0" step="0.01" value="<?= esc($target['current_value']) ?>"
                                   placeholder="0">
                            <div class="form-text">Current progress towards the target.</div>
                        </div>

                        <!-- Measurement Unit -->
                        <div class="col-md-4">
                            <label for="unit" class="form-label">
                                Measurement Unit <span class="required">*</span>
                            </label>
                            <select class="form-select" id="unit" name="unit" required>
                                <option value="">Select Unit</option>
                                <?php foreach ($measurement_units as $category => $units): ?>
                                    <optgroup label="<?= esc($category) ?>">
                                        <?php foreach ($units as $unit): ?>
                                            <option value="<?= esc($unit) ?>" <?= $unit === $target['unit'] ? 'selected' : '' ?>>
                                                <?= esc($unit) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </optgroup>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Unit of measurement for this target.</div>
                        </div>

                        <!-- Target Date -->
                        <div class="col-md-6">
                            <label for="target_date" class="form-label">
                                Target Date <span class="required">*</span>
                            </label>
                            <input type="date" class="form-control" id="target_date" name="target_date" required
                                   value="<?= esc($target['target_date']) ?>">
                            <div class="form-text">When should this target be achieved?</div>
                        </div>

                        <!-- Priority -->
                        <div class="col-md-6">
                            <label for="priority" class="form-label">
                                Priority <span class="required">*</span>
                            </label>
                            <select class="form-select" id="priority" name="priority" required>
                                <option value="">Select Priority</option>
                                <option value="High" <?= $target['priority'] === 'High' ? 'selected' : '' ?>>High</option>
                                <option value="Medium" <?= $target['priority'] === 'Medium' ? 'selected' : '' ?>>Medium</option>
                                <option value="Low" <?= $target['priority'] === 'Low' ? 'selected' : '' ?>>Low</option>
                            </select>
                            <div class="form-text">Set the priority level for this target.</div>
                        </div>

                        <!-- Status -->
                        <div class="col-md-6">
                            <label for="status" class="form-label">
                                Status <span class="required">*</span>
                            </label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="Pending" <?= $target['status'] === 'Pending' ? 'selected' : '' ?>>Pending</option>
                                <option value="In Progress" <?= $target['status'] === 'In Progress' ? 'selected' : '' ?>>In Progress</option>
                                <option value="Completed" <?= $target['status'] === 'Completed' ? 'selected' : '' ?>>Completed</option>
                            </select>
                            <div class="form-text">Update the status of the target.</div>
                        </div>

                        <!-- Completion Percentage -->
                        <div class="col-md-6">
                            <label for="completion_percentage" class="form-label">
                                Completion Percentage
                            </label>
                            <input type="number" class="form-control" id="completion_percentage" name="completion_percentage"
                                   min="0" max="100" value="<?= esc($target['completion_percentage']) ?>"
                                   placeholder="0">
                            <div class="form-text">Current completion percentage (0-100%).</div>
                        </div>

                        <!-- Description -->
                        <div class="col-12">
                            <label for="description" class="form-label">
                                Description <span class="required">*</span>
                            </label>
                            <textarea class="form-control" id="description" name="description" rows="4" required
                                      placeholder="Enter detailed description of the target..."><?= esc($target['description']) ?></textarea>
                            <div class="form-text">Provide a detailed description of what this target involves.</div>
                        </div>

                        <!-- Success Criteria -->
                        <div class="col-12">
                            <label for="success_criteria" class="form-label">
                                Success Criteria
                            </label>
                            <textarea class="form-control" id="success_criteria" name="success_criteria" rows="3"
                                      placeholder="Define what success looks like for this target...">Target will be considered successful when <?= esc($target['name']) ?> is completed with <?= esc($target['target_value']) ?> <?= esc($target['unit']) ?> achieved by <?= date('M d, Y', strtotime($target['target_date'])) ?>. Quality standards must be maintained throughout the process.</textarea>
                            <div class="form-text">Define the criteria that will determine if this target is successfully achieved.</div>
                        </div>

                        <!-- Remarks -->
                        <div class="col-12">
                            <label for="remarks" class="form-label">
                                Remarks
                            </label>
                            <textarea class="form-control" id="remarks" name="remarks" rows="2"
                                      placeholder="Enter any additional remarks or notes...">Target updated on <?= date('M d, Y') ?>. Current progress: <?= esc($target['completion_percentage']) ?>%. Status: <?= esc($target['status']) ?>. Priority level: <?= esc($target['priority']) ?>.</textarea>
                            <div class="form-text">Optional remarks or additional notes.</div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-2"></i>Update Target
                                </button>
                                <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities/' . $activity['id'] . '/targets') ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Form validation
        const form = document.querySelector('form');
        const targetDateInput = document.getElementById('target_date');
        const targetValueInput = document.getElementById('target_value');
        const currentValueInput = document.getElementById('current_value');
        const completionPercentageInput = document.getElementById('completion_percentage');

        // Auto-calculate completion percentage when values change
        function calculateCompletion() {
            const targetValue = parseFloat(targetValueInput.value) || 0;
            const currentValue = parseFloat(currentValueInput.value) || 0;
            
            if (targetValue > 0) {
                const percentage = Math.min(Math.round((currentValue / targetValue) * 100), 100);
                completionPercentageInput.value = percentage;
            }
        }

        targetValueInput.addEventListener('input', calculateCompletion);
        currentValueInput.addEventListener('input', calculateCompletion);

        // Validate current value doesn't exceed target value
        currentValueInput.addEventListener('input', function() {
            const targetValue = parseFloat(targetValueInput.value) || 0;
            const currentValue = parseFloat(this.value) || 0;
            
            if (targetValue > 0 && currentValue > targetValue) {
                this.setCustomValidity('Current value cannot exceed target value');
            } else {
                this.setCustomValidity('');
            }
        });

        // Form submission validation
        form.addEventListener('submit', function(e) {
            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';
            submitBtn.disabled = true;

            // Re-enable button after 3 seconds (in case of validation errors)
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 3000);
        });

        console.log('Edit Target form initialized successfully');
    });
</script>
<?= $this->endSection() ?>
