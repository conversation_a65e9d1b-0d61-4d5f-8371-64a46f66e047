<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .admin-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.12);
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .table th {
        background-color: #f8f9fa;
        border-top: none;
        color: #800000;
        font-weight: 600;
    }

    .btn-action {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        margin: 0 0.125rem;
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .budget-amount {
        font-family: 'Courier New', monospace;
        font-weight: bold;
        color: #28a745;
    }

    .stats-card {
        background: linear-gradient(135deg, #800000 0%, #a00000 100%);
        border-radius: 10px;
        color: white;
        transition: all 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(128, 0, 0, 0.3);
    }

    .stats-card .stats-number {
        font-size: 2rem;
        font-weight: bold;
        color: #28a745;
    }

    .budget-card {
        border: 1px solid #e9ecef;
        border-radius: 10px;
        transition: all 0.3s ease;
        background: white;
    }

    .budget-card:hover {
        border-color: #800000;
        box-shadow: 0 4px 12px rgba(128, 0, 0, 0.1);
    }

    .approval-info {
        background-color: #f8f9fa;
        border-radius: 6px;
        padding: 0.5rem;
        font-size: 0.85rem;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-book me-2"></i>Budget Books Management
                        </h3>
                        <p class="text-muted mb-0">
                            Manage budget books and their associated budget codes with allocated amounts.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="btn-group">
                            <a href="<?= site_url('admin') ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Admin
                            </a>
                            <a href="<?= site_url('admin/budget-books/create') ?>" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>Create Budget Book
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card p-3 text-center">
                <div class="stats-number"><?= $stats['total_budget_books'] ?></div>
                <div class="small">Total Budget Books</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card p-3 text-center">
                <div class="stats-number"><?= $stats['active_budgets'] ?></div>
                <div class="small">Active Budgets</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card p-3 text-center">
                <div class="stats-number"><?= $stats['draft_budgets'] + $stats['under_review'] ?></div>
                <div class="small">Pending Approval</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card p-3 text-center">
                <div class="stats-number">K <?= number_format($stats['total_allocated'] / 1000000000, 1) ?>B</div>
                <div class="small">Total Allocated</div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Budget Books List -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="fw-bold mb-0" style="color: #800000;">
                        <i class="fas fa-list me-2"></i>Budget Books
                    </h5>
                    <span class="badge bg-info">
                        <?= count($budget_books) ?> Budget Book<?= count($budget_books) !== 1 ? 's' : '' ?>
                    </span>
                </div>

                <?php if (empty($budget_books)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-book fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Budget Books Found</h5>
                        <p class="text-muted">Create your first budget book to get started.</p>
                        <a href="<?= site_url('admin/budget-books/create') ?>" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>Create First Budget Book
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Budget Book Name</th>
                                    <th>Fiscal Year</th>
                                    <th>Total Budget (PGK)</th>
                                    <th>Status</th>
                                    <th>Approval Info</th>
                                    <th>Budget Codes</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($budget_books as $book): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?= esc($book['name']) ?></strong>
                                            <br>
                                            <small class="text-muted"><?= esc($book['description']) ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary"><?= $book['fiscal_year'] ?></span>
                                    </td>
                                    <td>
                                        <span class="budget-amount">
                                            K <?= number_format($book['total_budget'] / 1000000, 0) ?>M
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= 
                                            $book['status'] === 'Active' ? 'success' : 
                                            ($book['status'] === 'Draft' ? 'secondary' : 'warning') 
                                        ?> status-badge">
                                            <?= esc($book['status']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($book['approved_at']): ?>
                                            <div class="approval-info">
                                                <div class="text-success">
                                                    <i class="fas fa-check-circle me-1"></i>
                                                    <strong>Approved</strong>
                                                </div>
                                                <small class="text-muted">
                                                    <?= date('M d, Y', strtotime($book['approved_at'])) ?><br>
                                                    by <?= esc($book['approved_by']) ?>
                                                </small>
                                            </div>
                                        <?php else: ?>
                                            <div class="approval-info">
                                                <div class="text-warning">
                                                    <i class="fas fa-clock me-1"></i>
                                                    <strong>Pending Approval</strong>
                                                </div>
                                                <small class="text-muted">
                                                    Created: <?= date('M d, Y', strtotime($book['created_at'])) ?>
                                                </small>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="<?= site_url('admin/budget-books/' . $book['id'] . '/codes') ?>" 
                                           class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-code me-1"></i>View Codes
                                        </a>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= site_url('admin/budget-books/' . $book['id'] . '/edit') ?>" 
                                               class="btn btn-outline-primary btn-action" title="Edit Budget Book">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form method="POST" action="<?= site_url('admin/budget-books/' . $book['id']) ?>" class="d-inline">
                                                <?= csrf_field() ?>
                                                <input type="hidden" name="_method" value="DELETE">
                                                <button type="submit" class="btn btn-outline-danger btn-action" 
                                                        title="Delete Budget Book"
                                                        onclick="return confirm('Are you sure you want to delete this budget book? This will also delete all associated budget codes.')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Navigation Help -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="admin-card p-3">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h6 class="mb-1 text-primary">
                            <i class="fas fa-info-circle me-2"></i>Navigation Flow
                        </h6>
                        <p class="mb-0 text-muted small">
                            Budget Books → Budget Codes with Amounts. Click "View Codes" to manage budget codes and their allocated amounts within each budget book.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= site_url('admin') ?>" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-arrow-left me-2"></i>Back to Admin Settings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-hide alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            setTimeout(function() {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, 5000);
        });

        console.log('Budget Books Management initialized successfully');
    });
</script>
<?= $this->endSection() ?>
