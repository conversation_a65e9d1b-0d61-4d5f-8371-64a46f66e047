<?php

namespace App\Controllers;

class AdminLegislationController extends BaseController
{
    /**
     * Display legislation list (GET request)
     *
     * @return string
     */
    public function index()
    {
        $data = [
            'page_title' => 'Legislation Management',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Legislated Activities', 'url' => site_url('admin/legislated-activities')],
                ['title' => 'Legislation Management', 'url' => '']
            ],
            'legislation' => $this->getDummyLegislation(),
            'stats' => $this->getLegislationStats()
        ];

        return view('admin/admin_legislation_index', $data);
    }

    /**
     * Show create legislation form (GET request)
     *
     * @return string
     */
    public function create()
    {
        $data = [
            'page_title' => 'Create Legislation',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Legislated Activities', 'url' => site_url('admin/legislated-activities')],
                ['title' => 'Legislation Management', 'url' => site_url('admin/legislation')],
                ['title' => 'Create Legislation', 'url' => '']
            ]
        ];

        return view('admin/admin_legislation_create', $data);
    }

    /**
     * Store new legislation (POST request)
     *
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function store()
    {
        // In real implementation, this would save to database
        // For now, just redirect with success message

        return redirect()->to(site_url('admin/legislation'))
                        ->with('success', 'Legislation created successfully!');
    }

    /**
     * Show edit legislation form (GET request)
     *
     * @param int $id
     * @return string
     */
    public function edit($id)
    {
        $legislation = $this->getDummyLegislationById($id);

        if (!$legislation) {
            return redirect()->to(site_url('admin/legislation'))
                            ->with('error', 'Legislation not found!');
        }

        $data = [
            'page_title' => 'Edit Legislation',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Legislated Activities', 'url' => site_url('admin/legislated-activities')],
                ['title' => 'Legislation Management', 'url' => site_url('admin/legislation')],
                ['title' => 'Edit Legislation', 'url' => '']
            ],
            'legislation' => $legislation
        ];

        return view('admin/admin_legislation_edit', $data);
    }

    /**
     * Update legislation (PUT request)
     *
     * @param int $id
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function update($id)
    {
        // In real implementation, this would update database
        // For now, just redirect with success message

        return redirect()->to(site_url('admin/legislation'))
                        ->with('success', 'Legislation updated successfully!');
    }

    /**
     * Delete legislation (DELETE request)
     *
     * @param int $id
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function delete($id)
    {
        // In real implementation, this would delete from database
        // For now, just redirect with success message

        return redirect()->to(site_url('admin/legislation'))
                        ->with('success', 'Legislation deleted successfully!');
    }

    /**
     * Display activities for specific legislation (GET request)
     *
     * @param int $legislationId
     * @return string
     */
    public function activities($legislationId)
    {
        $legislation = $this->getDummyLegislationById($legislationId);

        if (!$legislation) {
            return redirect()->to(site_url('admin/legislation'))
                            ->with('error', 'Legislation not found!');
        }

        $data = [
            'page_title' => 'Activities - ' . $legislation['name'],
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Legislated Activities', 'url' => site_url('admin/legislated-activities')],
                ['title' => 'Legislation Management', 'url' => site_url('admin/legislation')],
                ['title' => $legislation['name'], 'url' => '']
            ],
            'legislation' => $legislation,
            'activities' => $this->getDummyActivitiesByLegislation($legislationId),
            'groups' => $this->getDummyGroups()
        ];

        return view('admin/admin_legislation_activities', $data);
    }

    /**
     * Show create activity form (GET request)
     *
     * @param int $legislationId
     * @return string
     */
    public function createActivity($legislationId)
    {
        $legislation = $this->getDummyLegislationById($legislationId);

        if (!$legislation) {
            return redirect()->to(site_url('admin/legislation'))
                            ->with('error', 'Legislation not found!');
        }

        $data = [
            'page_title' => 'Create Activity',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Legislated Activities', 'url' => site_url('admin/legislated-activities')],
                ['title' => 'Legislation Management', 'url' => site_url('admin/legislation')],
                ['title' => $legislation['name'], 'url' => site_url('admin/legislation/' . $legislationId . '/activities')],
                ['title' => 'Create Activity', 'url' => '']
            ],
            'legislation' => $legislation,
            'groups' => $this->getDummyGroups()
        ];

        return view('admin/admin_legislation_activity_create', $data);
    }

    /**
     * Store new activity (POST request)
     *
     * @param int $legislationId
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function storeActivity($legislationId)
    {
        // In real implementation, this would save to database
        // For now, just redirect with success message

        return redirect()->to(site_url('admin/legislation/' . $legislationId . '/activities'))
                        ->with('success', 'Activity created successfully!');
    }

    /**
     * Show edit activity form (GET request)
     *
     * @param int $legislationId
     * @param int $activityId
     * @return string
     */
    public function editActivity($legislationId, $activityId)
    {
        $legislation = $this->getDummyLegislationById($legislationId);
        $activity = $this->getDummyActivityById($activityId);

        if (!$legislation || !$activity) {
            return redirect()->to(site_url('admin/legislation/' . $legislationId . '/activities'))
                            ->with('error', 'Legislation or Activity not found!');
        }

        $data = [
            'page_title' => 'Edit Activity',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Legislated Activities', 'url' => site_url('admin/legislated-activities')],
                ['title' => 'Legislation Management', 'url' => site_url('admin/legislation')],
                ['title' => $legislation['name'], 'url' => site_url('admin/legislation/' . $legislationId . '/activities')],
                ['title' => 'Edit Activity', 'url' => '']
            ],
            'legislation' => $legislation,
            'activity' => $activity,
            'groups' => $this->getDummyGroups()
        ];

        return view('admin/admin_legislation_activity_edit', $data);
    }

    /**
     * Update activity (PUT request)
     *
     * @param int $legislationId
     * @param int $activityId
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function updateActivity($legislationId, $activityId)
    {
        // In real implementation, this would update database
        // For now, just redirect with success message

        return redirect()->to(site_url('admin/legislation/' . $legislationId . '/activities'))
                        ->with('success', 'Activity updated successfully!');
    }

    /**
     * Delete activity (DELETE request)
     *
     * @param int $legislationId
     * @param int $activityId
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function deleteActivity($legislationId, $activityId)
    {
        // In real implementation, this would delete from database
        // For now, just redirect with success message

        return redirect()->to(site_url('admin/legislation/' . $legislationId . '/activities'))
                        ->with('success', 'Activity deleted successfully!');
    }

    // ========================================
    // DUMMY DATA METHODS
    // ========================================

    /**
     * Get dummy legislation data
     *
     * @return array
     */
    private function getDummyLegislation()
    {
        return [
            [
                'id' => 1,
                'name' => 'Public Finance Management Act 1995',
                'reference_number' => 'PFMA-1995-001',
                'description' => 'An Act to provide for the management of public finances, including the preparation of budgets, accounting for public money, and financial reporting.',
                'date_enacted' => '1995-06-15',
                'status' => 'Active',
                'activities_count' => 8,
                'created_at' => '2024-01-15 10:30:00',
                'updated_at' => '2024-01-15 10:30:00'
            ],
            [
                'id' => 2,
                'name' => 'Audit Act 1989',
                'reference_number' => 'AUD-1989-002',
                'description' => 'An Act to establish the Office of the Auditor-General and to provide for the audit of public accounts and the accounts of statutory authorities.',
                'date_enacted' => '1989-03-20',
                'status' => 'Active',
                'activities_count' => 6,
                'created_at' => '2024-01-15 11:00:00',
                'updated_at' => '2024-01-15 11:00:00'
            ],
            [
                'id' => 3,
                'name' => 'Procurement Act 2018',
                'reference_number' => 'PROC-2018-003',
                'description' => 'An Act to regulate public procurement processes and ensure transparency, accountability, and value for money in government purchases.',
                'date_enacted' => '2018-11-10',
                'status' => 'Active',
                'activities_count' => 12,
                'created_at' => '2024-01-15 11:30:00',
                'updated_at' => '2024-01-15 11:30:00'
            ],
            [
                'id' => 4,
                'name' => 'Anti-Corruption Act 2003',
                'reference_number' => 'ANTI-2003-004',
                'description' => 'An Act to prevent and combat corruption in public and private sectors, establishing the Independent Commission Against Corruption.',
                'date_enacted' => '2003-08-25',
                'status' => 'Active',
                'activities_count' => 10,
                'created_at' => '2024-01-15 12:00:00',
                'updated_at' => '2024-01-15 12:00:00'
            ],
            [
                'id' => 5,
                'name' => 'Environmental Protection Act 2000',
                'reference_number' => 'ENV-2000-005',
                'description' => 'An Act to protect and conserve the environment, regulate environmental impact assessments, and establish environmental standards.',
                'date_enacted' => '2000-04-22',
                'status' => 'Active',
                'activities_count' => 15,
                'created_at' => '2024-01-15 12:30:00',
                'updated_at' => '2024-01-15 12:30:00'
            ],
            [
                'id' => 6,
                'name' => 'Public Service Management Act 2014',
                'reference_number' => 'PSM-2014-006',
                'description' => 'An Act to modernize public service management, establish performance standards, and regulate employment in the public sector.',
                'date_enacted' => '2014-09-12',
                'status' => 'Active',
                'activities_count' => 9,
                'created_at' => '2024-01-15 13:00:00',
                'updated_at' => '2024-01-15 13:00:00'
            ]
        ];
    }

    /**
     * Get legislation statistics
     *
     * @return array
     */
    private function getLegislationStats()
    {
        $legislation = $this->getDummyLegislation();
        $totalActivities = array_sum(array_column($legislation, 'activities_count'));

        return [
            'total_legislation' => count($legislation),
            'total_activities' => $totalActivities,
            'active_legislation' => count(array_filter($legislation, function($l) { return $l['status'] === 'Active'; })),
            'avg_activities_per_legislation' => round($totalActivities / count($legislation), 1)
        ];
    }

    /**
     * Get dummy legislation by ID
     *
     * @param int $id
     * @return array|null
     */
    private function getDummyLegislationById($id)
    {
        $legislation = $this->getDummyLegislation();
        foreach ($legislation as $law) {
            if ($law['id'] == $id) {
                return $law;
            }
        }
        return null;
    }

    /**
     * Get dummy activities by legislation ID
     *
     * @param int $legislationId
     * @return array
     */
    private function getDummyActivitiesByLegislation($legislationId)
    {
        $allActivities = [
            1 => [ // Public Finance Management Act 1995
                [
                    'id' => 1,
                    'legislation_id' => 1,
                    'name' => 'Annual Budget Preparation',
                    'description' => 'Prepare and submit annual budget estimates to the Department of Treasury',
                    'frequency' => 'Annual',
                    'due_period' => 'October 31',
                    'priority' => 'Critical',
                    'linked_groups' => [1, 2],
                    'created_at' => '2024-01-15 14:00:00'
                ],
                [
                    'id' => 2,
                    'legislation_id' => 1,
                    'name' => 'Monthly Financial Reporting',
                    'description' => 'Submit monthly financial statements and variance reports',
                    'frequency' => 'Monthly',
                    'due_period' => '15th of following month',
                    'priority' => 'High',
                    'linked_groups' => [1],
                    'created_at' => '2024-01-15 14:15:00'
                ],
                [
                    'id' => 3,
                    'legislation_id' => 1,
                    'name' => 'Quarterly Budget Review',
                    'description' => 'Review and analyze quarterly budget performance and submit reports',
                    'frequency' => 'Quarterly',
                    'due_period' => 'End of quarter + 30 days',
                    'priority' => 'High',
                    'linked_groups' => [1, 2],
                    'created_at' => '2024-01-15 14:30:00'
                ]
            ],
            2 => [ // Audit Act 1989
                [
                    'id' => 4,
                    'legislation_id' => 2,
                    'name' => 'Internal Audit Planning',
                    'description' => 'Develop and submit annual internal audit plan to Auditor-General',
                    'frequency' => 'Annual',
                    'due_period' => 'December 31',
                    'priority' => 'Critical',
                    'linked_groups' => [3],
                    'created_at' => '2024-01-15 14:45:00'
                ],
                [
                    'id' => 5,
                    'legislation_id' => 2,
                    'name' => 'Audit Compliance Reporting',
                    'description' => 'Submit compliance reports on audit recommendations implementation',
                    'frequency' => 'Quarterly',
                    'due_period' => 'End of quarter + 15 days',
                    'priority' => 'High',
                    'linked_groups' => [3, 4],
                    'created_at' => '2024-01-15 15:00:00'
                ]
            ],
            3 => [ // Procurement Act 2018
                [
                    'id' => 6,
                    'legislation_id' => 3,
                    'name' => 'Procurement Planning',
                    'description' => 'Develop annual procurement plan and submit to Central Supply and Tenders Board',
                    'frequency' => 'Annual',
                    'due_period' => 'November 30',
                    'priority' => 'Critical',
                    'linked_groups' => [5],
                    'created_at' => '2024-01-15 15:15:00'
                ],
                [
                    'id' => 7,
                    'legislation_id' => 3,
                    'name' => 'Tender Documentation',
                    'description' => 'Prepare and publish tender documents for procurement activities',
                    'frequency' => 'As Required',
                    'due_period' => 'Within 30 days of approval',
                    'priority' => 'High',
                    'linked_groups' => [5, 6],
                    'created_at' => '2024-01-15 15:30:00'
                ]
            ]
        ];

        return $allActivities[$legislationId] ?? [];
    }

    /**
     * Get dummy activity by ID
     *
     * @param int $id
     * @return array|null
     */
    private function getDummyActivityById($id)
    {
        // Get all activities from all legislation
        $allActivities = [];
        for ($i = 1; $i <= 6; $i++) {
            $activities = $this->getDummyActivitiesByLegislation($i);
            $allActivities = array_merge($allActivities, $activities);
        }

        foreach ($allActivities as $activity) {
            if ($activity['id'] == $id) {
                return $activity;
            }
        }
        return null;
    }

    /**
     * Get dummy groups data
     *
     * @return array
     */
    private function getDummyGroups()
    {
        return [
            [
                'id' => 1,
                'name' => 'Finance and Administration',
                'description' => 'Responsible for financial management and administrative functions',
                'structure_id' => 1,
                'head_position' => 'Chief Financial Officer',
                'status' => 'Active'
            ],
            [
                'id' => 2,
                'name' => 'Budget and Planning',
                'description' => 'Handles budget preparation, monitoring and strategic planning',
                'structure_id' => 1,
                'head_position' => 'Budget Director',
                'status' => 'Active'
            ],
            [
                'id' => 3,
                'name' => 'Internal Audit',
                'description' => 'Conducts internal audits and ensures compliance',
                'structure_id' => 1,
                'head_position' => 'Chief Internal Auditor',
                'status' => 'Active'
            ],
            [
                'id' => 4,
                'name' => 'Risk Management',
                'description' => 'Manages organizational risks and compliance monitoring',
                'structure_id' => 1,
                'head_position' => 'Risk Manager',
                'status' => 'Active'
            ],
            [
                'id' => 5,
                'name' => 'Procurement and Supply',
                'description' => 'Manages procurement processes and supply chain',
                'structure_id' => 1,
                'head_position' => 'Procurement Manager',
                'status' => 'Active'
            ],
            [
                'id' => 6,
                'name' => 'Legal and Compliance',
                'description' => 'Provides legal advice and ensures regulatory compliance',
                'structure_id' => 1,
                'head_position' => 'Legal Counsel',
                'status' => 'Active'
            ]
        ];
    }
}