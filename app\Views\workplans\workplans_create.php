<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .admin-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.12);
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .form-label {
        font-weight: 600;
        color: #800000;
    }

    .form-control:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    .form-select:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    .btn-success {
        background-color: #28a745;
        border-color: #28a745;
    }

    .btn-success:hover {
        background-color: #218838;
        border-color: #1e7e34;
    }

    .required {
        color: #dc3545;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-plus-circle me-2"></i>Create New Workplan
                        </h3>
                        <p class="text-muted mb-0">
                            Create a new workplan for a specific position and fiscal year. You can add activities after creating the workplan.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= site_url('workplan-management') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Workplans
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Form -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <h5 class="fw-bold mb-4" style="color: #800000;">
                    <i class="fas fa-clipboard-list me-2"></i>Workplan Information
                </h5>

                <form action="<?= site_url('workplan-management') ?>" method="POST">
                    <?= csrf_field() ?>
                    
                    <div class="row g-4">
                        <!-- Position Selection -->
                        <div class="col-md-6">
                            <label for="position_id" class="form-label">
                                Position <span class="required">*</span>
                            </label>
                            <select class="form-select" id="position_id" name="position_id" required>
                                <option value="">Select Position</option>
                                <?php foreach ($positions as $position): ?>
                                    <option value="<?= $position['id'] ?>" data-group="<?= esc($position['group']) ?>">
                                        <?= esc($position['name']) ?> (<?= esc($position['group']) ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Select the position for which this workplan is being created.</div>
                        </div>

                        <!-- Supervisor Position Selection -->
                        <div class="col-md-6">
                            <label for="supervisor_position_id" class="form-label">
                                Supervisor Position <span class="required">*</span>
                            </label>
                            <select class="form-select" id="supervisor_position_id" name="supervisor_position_id" required>
                                <option value="">Select Supervisor Position</option>
                                <?php foreach ($positions as $position): ?>
                                    <option value="<?= $position['id'] ?>">
                                        <?= esc($position['name']) ?> (<?= esc($position['group']) ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Select the supervisor position for this workplan.</div>
                        </div>

                        <!-- Fiscal Year -->
                        <div class="col-md-6">
                            <label for="fiscal_year" class="form-label">
                                Fiscal Year <span class="required">*</span>
                            </label>
                            <select class="form-select" id="fiscal_year" name="fiscal_year" required>
                                <option value="">Select Fiscal Year</option>
                                <?php foreach ($fiscal_years as $year): ?>
                                    <option value="<?= $year ?>" <?= $year == date('Y') ? 'selected' : '' ?>>
                                        <?= $year ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Select the fiscal year for this workplan.</div>
                        </div>

                        <!-- Status -->
                        <div class="col-md-6">
                            <label for="status" class="form-label">
                                Status <span class="required">*</span>
                            </label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="Active" selected>Active</option>
                                <option value="Draft">Draft</option>
                                <option value="Completed">Completed</option>
                            </select>
                            <div class="form-text">Set the initial status of the workplan.</div>
                        </div>

                        <!-- Description -->
                        <div class="col-12">
                            <label for="description" class="form-label">
                                Description
                            </label>
                            <textarea class="form-control" id="description" name="description" rows="4" 
                                      placeholder="Enter workplan description or objectives..."></textarea>
                            <div class="form-text">Optional description or objectives for this workplan.</div>
                        </div>

                        <!-- Remarks -->
                        <div class="col-12">
                            <label for="remarks" class="form-label">
                                Remarks
                            </label>
                            <textarea class="form-control" id="remarks" name="remarks" rows="3" 
                                      placeholder="Enter any additional remarks or notes..."></textarea>
                            <div class="form-text">Optional remarks or additional notes.</div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-2"></i>Create Workplan
                                </button>
                                <a href="<?= site_url('workplan-management') ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Information Card -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <h6 class="fw-bold text-info mb-3">
                    <i class="fas fa-info-circle me-2"></i>Next Steps
                </h6>
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                                 style="width: 30px; height: 30px; font-size: 14px;">1</div>
                            <div>
                                <h6 class="mb-1">Create Workplan</h6>
                                <small class="text-muted">Set up the basic workplan information</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                                 style="width: 30px; height: 30px; font-size: 14px;">2</div>
                            <div>
                                <h6 class="mb-1">Add Activities</h6>
                                <small class="text-muted">Create activities within the workplan</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                                 style="width: 30px; height: 30px; font-size: 14px;">3</div>
                            <div>
                                <h6 class="mb-1">Link Resources</h6>
                                <small class="text-muted">Link activities to plans, budget, assets, and officers</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Form validation
        const form = document.querySelector('form');
        const positionSelect = document.getElementById('position_id');
        const supervisorSelect = document.getElementById('supervisor_position_id');

        // Prevent selecting the same position as supervisor
        positionSelect.addEventListener('change', function() {
            const selectedPositionId = this.value;
            const supervisorOptions = supervisorSelect.querySelectorAll('option');
            
            supervisorOptions.forEach(option => {
                if (option.value === selectedPositionId && selectedPositionId !== '') {
                    option.disabled = true;
                    option.style.color = '#ccc';
                } else {
                    option.disabled = false;
                    option.style.color = '';
                }
            });

            // Reset supervisor selection if it matches the selected position
            if (supervisorSelect.value === selectedPositionId) {
                supervisorSelect.value = '';
            }
        });

        // Form submission validation
        form.addEventListener('submit', function(e) {
            const positionId = positionSelect.value;
            const supervisorId = supervisorSelect.value;

            if (positionId === supervisorId && positionId !== '') {
                e.preventDefault();
                alert('Position and Supervisor Position cannot be the same.');
                return false;
            }

            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';
            submitBtn.disabled = true;

            // Re-enable button after 3 seconds (in case of validation errors)
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 3000);
        });

        console.log('Create Workplan form initialized successfully');
    });
</script>
<?= $this->endSection() ?>
