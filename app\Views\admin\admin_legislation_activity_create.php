<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .admin-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.12);
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .form-label {
        font-weight: 600;
        color: #800000;
    }

    .form-control:focus {
        border-color: #800000;
        box-shadow: 0 0 0 0.2rem rgba(128, 0, 0, 0.25);
    }

    .form-select:focus {
        border-color: #800000;
        box-shadow: 0 0 0 0.2rem rgba(128, 0, 0, 0.25);
    }

    .btn-primary {
        background-color: #800000;
        border-color: #800000;
    }

    .btn-primary:hover {
        background-color: #28a745;
        border-color: #28a745;
    }

    .required {
        color: #dc3545;
    }

    .legislation-info {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .group-checkbox {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
    }

    .group-checkbox:hover {
        border-color: #800000;
        background-color: #f8f9fa;
    }

    .group-checkbox input:checked + label {
        color: #800000;
        font-weight: 600;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-plus-circle me-2"></i>Create New Activity
                        </h3>
                        <p class="text-muted mb-0">
                            Add a new mandatory activity under the legislation: <strong><?= esc($legislation['name']) ?></strong>
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= site_url('admin/legislation/' . $legislation['id'] . '/activities') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Activities
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Legislation Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="legislation-info">
                <h6 class="fw-bold text-primary mb-2">
                    <i class="fas fa-balance-scale me-2"></i>Legislation: <?= esc($legislation['name']) ?>
                </h6>
                <p class="mb-2"><?= esc($legislation['description']) ?></p>
                <div class="d-flex gap-3">
                    <span class="badge bg-primary"><?= esc($legislation['reference_number']) ?></span>
                    <span class="badge bg-success"><?= esc($legislation['status']) ?></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Form -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <form method="post" action="<?= site_url('admin/legislation/' . $legislation['id'] . '/activities') ?>">
                    <?= csrf_field() ?>
                    
                    <div class="row">
                        <div class="col-lg-8">
                            <!-- Activity Information -->
                            <h5 class="fw-bold mb-3" style="color: #800000;">
                                <i class="fas fa-info-circle me-2"></i>Activity Information
                            </h5>
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    Activity Name <span class="required">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="name" 
                                       name="name" 
                                       placeholder="Enter the name of the mandatory activity"
                                       required>
                                <div class="form-text">Enter a clear and descriptive name for the activity</div>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">
                                    Description <span class="required">*</span>
                                </label>
                                <textarea class="form-control" 
                                          id="description" 
                                          name="description" 
                                          rows="3" 
                                          placeholder="Describe what this activity involves and its requirements"
                                          required></textarea>
                                <div class="form-text">Provide detailed information about the activity requirements</div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="frequency" class="form-label">
                                            Frequency <span class="required">*</span>
                                        </label>
                                        <select class="form-select" id="frequency" name="frequency" required>
                                            <option value="">Select Frequency</option>
                                            <option value="Daily">Daily</option>
                                            <option value="Weekly">Weekly</option>
                                            <option value="Monthly">Monthly</option>
                                            <option value="Quarterly">Quarterly</option>
                                            <option value="Semi-Annual">Semi-Annual</option>
                                            <option value="Annual">Annual</option>
                                            <option value="As Required">As Required</option>
                                        </select>
                                        <div class="form-text">How often this activity must be performed</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="priority" class="form-label">
                                            Priority <span class="required">*</span>
                                        </label>
                                        <select class="form-select" id="priority" name="priority" required>
                                            <option value="">Select Priority</option>
                                            <option value="Critical">Critical</option>
                                            <option value="High">High</option>
                                            <option value="Medium">Medium</option>
                                            <option value="Low">Low</option>
                                        </select>
                                        <div class="form-text">Priority level of this activity</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="due_period" class="form-label">
                                            Due Period <span class="required">*</span>
                                        </label>
                                        <input type="text" 
                                               class="form-control" 
                                               id="due_period" 
                                               name="due_period" 
                                               placeholder="e.g., End of month + 15 days"
                                               required>
                                        <div class="form-text">When this activity is due</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <!-- Group Assignment -->
                            <h5 class="fw-bold mb-3" style="color: #800000;">
                                <i class="fas fa-users me-2"></i>Link to Groups
                            </h5>
                            
                            <p class="text-muted mb-3">Select which organizational groups must perform this activity:</p>
                            
                            <div class="mb-3">
                                <?php foreach ($groups as $group): ?>
                                <div class="group-checkbox">
                                    <div class="form-check">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               value="<?= $group['id'] ?>" 
                                               id="group_<?= $group['id'] ?>" 
                                               name="linked_groups[]">
                                        <label class="form-check-label" for="group_<?= $group['id'] ?>">
                                            <strong><?= esc($group['name']) ?></strong>
                                            <br><small class="text-muted"><?= esc($group['description']) ?></small>
                                        </label>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>

                            <div class="alert alert-info">
                                <h6 class="alert-heading">
                                    <i class="fas fa-lightbulb me-2"></i>Group Linking
                                </h6>
                                <p class="mb-0">
                                    Select the groups that are responsible for performing this activity. 
                                    You can select multiple groups if the activity applies to several departments.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="border-top pt-3">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <a href="<?= site_url('admin/legislation/' . $legislation['id'] . '/activities') ?>" class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-2"></i>Cancel
                                        </a>
                                    </div>
                                    <div>
                                        <button type="reset" class="btn btn-outline-warning me-2">
                                            <i class="fas fa-undo me-2"></i>Reset Form
                                        </button>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Create Activity
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Form validation
        const form = document.querySelector('form');

        // Auto-suggest due period based on frequency
        document.getElementById('frequency').addEventListener('change', function() {
            const duePeriodInput = document.getElementById('due_period');
            const frequency = this.value;
            
            if (!duePeriodInput.value) {
                switch(frequency) {
                    case 'Daily':
                        duePeriodInput.value = 'End of each day';
                        break;
                    case 'Weekly':
                        duePeriodInput.value = 'End of each week';
                        break;
                    case 'Monthly':
                        duePeriodInput.value = '15th of following month';
                        break;
                    case 'Quarterly':
                        duePeriodInput.value = 'End of quarter + 30 days';
                        break;
                    case 'Annual':
                        duePeriodInput.value = 'December 31';
                        break;
                    case 'As Required':
                        duePeriodInput.value = 'Within 30 days of trigger';
                        break;
                }
            }
        });

        // Form submission validation
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            // Check if at least one group is selected
            const groupCheckboxes = form.querySelectorAll('input[name="linked_groups[]"]:checked');
            if (groupCheckboxes.length === 0) {
                alert('Please select at least one group for this activity.');
                isValid = false;
            }

            if (!isValid) {
                e.preventDefault();
                alert('Please fill in all required fields and select at least one group.');
            }
        });

        console.log('Create Activity form initialized successfully');
    });
</script>
<?= $this->endSection() ?>
