<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .admin-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.12);
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .form-label {
        font-weight: 600;
        color: #800000;
    }

    .form-control:focus {
        border-color: #800000;
        box-shadow: 0 0 0 0.2rem rgba(128, 0, 0, 0.25);
    }

    .form-select:focus {
        border-color: #800000;
        box-shadow: 0 0 0 0.2rem rgba(128, 0, 0, 0.25);
    }

    .btn-primary {
        background-color: #800000;
        border-color: #800000;
    }

    .btn-primary:hover {
        background-color: #28a745;
        border-color: #28a745;
    }

    .required {
        color: #dc3545;
    }

    .info-card {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-edit me-2"></i>Edit Legislation
                        </h3>
                        <p class="text-muted mb-0">
                            Update the details of the legislation: <strong><?= esc($legislation['name']) ?></strong>
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="btn-group">
                            <a href="<?= site_url('admin/legislation') ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Legislation
                            </a>
                            <a href="<?= site_url('admin/legislation/' . $legislation['id'] . '/activities') ?>" class="btn btn-outline-info">
                                <i class="fas fa-tasks me-2"></i>View Activities
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <form method="post" action="<?= site_url('admin/legislation/' . $legislation['id']) ?>">
                    <?= csrf_field() ?>
                    <input type="hidden" name="_method" value="PUT">
                    
                    <div class="row">
                        <div class="col-lg-8">
                            <!-- Basic Information -->
                            <h5 class="fw-bold mb-3" style="color: #800000;">
                                <i class="fas fa-info-circle me-2"></i>Basic Information
                            </h5>
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    Legislation Name <span class="required">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="name" 
                                       name="name" 
                                       value="<?= esc($legislation['name']) ?>"
                                       placeholder="Enter the full name of the legislation"
                                       required>
                                <div class="form-text">Enter the complete official name of the legislation</div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="reference_number" class="form-label">
                                            Reference Number <span class="required">*</span>
                                        </label>
                                        <input type="text" 
                                               class="form-control" 
                                               id="reference_number" 
                                               name="reference_number" 
                                               value="<?= esc($legislation['reference_number']) ?>"
                                               placeholder="e.g., PFMA-1995-001"
                                               required>
                                        <div class="form-text">Unique reference number for the legislation</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="date_enacted" class="form-label">
                                            Date Enacted <span class="required">*</span>
                                        </label>
                                        <input type="date" 
                                               class="form-control" 
                                               id="date_enacted" 
                                               name="date_enacted" 
                                               value="<?= esc($legislation['date_enacted']) ?>"
                                               required>
                                        <div class="form-text">Date when the legislation was enacted</div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">
                                    Description <span class="required">*</span>
                                </label>
                                <textarea class="form-control" 
                                          id="description" 
                                          name="description" 
                                          rows="4" 
                                          placeholder="Provide a detailed description of the legislation and its purpose"
                                          required><?= esc($legislation['description']) ?></textarea>
                                <div class="form-text">Describe the purpose and scope of the legislation</div>
                            </div>

                            <div class="mb-3">
                                <label for="status" class="form-label">
                                    Status <span class="required">*</span>
                                </label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="">Select Status</option>
                                    <option value="Active" <?= $legislation['status'] === 'Active' ? 'selected' : '' ?>>Active</option>
                                    <option value="Inactive" <?= $legislation['status'] === 'Inactive' ? 'selected' : '' ?>>Inactive</option>
                                    <option value="Under Review" <?= $legislation['status'] === 'Under Review' ? 'selected' : '' ?>>Under Review</option>
                                    <option value="Repealed" <?= $legislation['status'] === 'Repealed' ? 'selected' : '' ?>>Repealed</option>
                                </select>
                                <div class="form-text">Current status of the legislation</div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <!-- Legislation Information -->
                            <h5 class="fw-bold mb-3" style="color: #800000;">
                                <i class="fas fa-info me-2"></i>Legislation Information
                            </h5>
                            
                            <div class="info-card">
                                <h6 class="fw-bold text-primary">
                                    <i class="fas fa-calendar me-2"></i>Created
                                </h6>
                                <p class="mb-2"><?= date('M d, Y g:i A', strtotime($legislation['created_at'])) ?></p>
                                
                                <h6 class="fw-bold text-info">
                                    <i class="fas fa-edit me-2"></i>Last Updated
                                </h6>
                                <p class="mb-2"><?= date('M d, Y g:i A', strtotime($legislation['updated_at'])) ?></p>
                                
                                <h6 class="fw-bold text-success">
                                    <i class="fas fa-tasks me-2"></i>Activities
                                </h6>
                                <p class="mb-0"><?= $legislation['activities_count'] ?> activities defined</p>
                            </div>

                            <div class="alert alert-warning">
                                <h6 class="alert-heading">
                                    <i class="fas fa-exclamation-triangle me-2"></i>Important Note
                                </h6>
                                <p class="mb-0">
                                    Changes to this legislation may affect existing activities and workplans that reference it.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="border-top pt-3">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <a href="<?= site_url('admin/legislation') ?>" class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-2"></i>Cancel
                                        </a>
                                    </div>
                                    <div>
                                        <a href="<?= site_url('admin/legislation/' . $legislation['id'] . '/activities') ?>" class="btn btn-outline-info me-2">
                                            <i class="fas fa-tasks me-2"></i>Manage Activities
                                        </a>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Update Legislation
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Form validation
        const form = document.querySelector('form');

        // Form submission validation
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            if (!isValid) {
                e.preventDefault();
                alert('Please fill in all required fields.');
            }
        });

        // Date validation
        const dateInput = document.getElementById('date_enacted');
        dateInput.addEventListener('change', function() {
            const selectedDate = new Date(this.value);
            const today = new Date();
            
            if (selectedDate > today) {
                alert('Date enacted cannot be in the future.');
                this.value = '';
            }
        });

        console.log('Edit Legislation form initialized successfully');
    });
</script>
<?= $this->endSection() ?>
