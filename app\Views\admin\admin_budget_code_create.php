<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .form-label {
        color: #800000;
        font-weight: 600;
    }

    .form-control:focus, .form-select:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    .btn-success {
        background-color: #28a745;
        border-color: #28a745;
    }

    .btn-success:hover {
        background-color: #218838;
        border-color: #1e7e34;
    }

    .code-info {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .currency-input {
        position: relative;
    }

    .currency-symbol {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #28a745;
        font-weight: bold;
        z-index: 10;
    }

    .currency-input .form-control {
        padding-left: 35px;
    }

    .code-format {
        font-family: 'Courier New', monospace;
        background-color: #e9ecef;
        padding: 0.5rem;
        border-radius: 4px;
        font-size: 0.9rem;
    }

    .amount-section {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-plus-circle me-2"></i>Create New Budget Code
                        </h3>
                        <p class="text-muted mb-1">
                            Budget Book: <strong class="text-primary"><?= esc($budget_book['name']) ?></strong>
                        </p>
                        <p class="text-muted mb-0 small">
                            Create a new budget code with allocated amounts within this budget book.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= site_url('admin/budget-books/' . $budget_book['id'] . '/codes') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Budget Codes
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Form -->
    <div class="row">
        <div class="col-lg-10 mx-auto">
            <div class="admin-card p-4">
                <form method="POST" action="<?= site_url('admin/budget-books/' . $budget_book['id'] . '/codes') ?>" id="createBudgetCodeForm">
                    <?= csrf_field() ?>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-4">
                                <label for="code" class="form-label">
                                    <i class="fas fa-hashtag me-2"></i>Budget Code *
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="code" 
                                       name="code" 
                                       placeholder="e.g., 01.01.001"
                                       required
                                       maxlength="20"
                                       pattern="[0-9]{2}\.[0-9]{2}\.[0-9]{3}">
                                <div class="form-text">
                                    Format: XX.XX.XXX (e.g., 01.01.001)
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="mb-4">
                                <label for="name" class="form-label">
                                    <i class="fas fa-tag me-2"></i>Budget Code Name *
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="name" 
                                       name="name" 
                                       placeholder="Enter budget code name (e.g., Personnel Emoluments - Salaries)"
                                       required
                                       maxlength="255">
                                <div class="form-text">
                                    Enter a descriptive name for this budget code.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="description" class="form-label">
                            <i class="fas fa-align-left me-2"></i>Description
                        </label>
                        <textarea class="form-control" 
                                  id="description" 
                                  name="description" 
                                  rows="3"
                                  placeholder="Enter a detailed description of what this budget code covers"
                                  maxlength="500"></textarea>
                        <div class="form-text">
                            Provide a detailed description of the budget code's purpose and coverage.
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="category" class="form-label">
                            <i class="fas fa-folder me-2"></i>Category *
                        </label>
                        <select class="form-select" id="category" name="category" required>
                            <option value="">Select Budget Category</option>
                            <?php foreach ($budget_categories as $key => $value): ?>
                                <option value="<?= esc($key) ?>"><?= esc($value) ?></option>
                            <?php endforeach; ?>
                        </select>
                        <div class="form-text">
                            Select the appropriate category for this budget code.
                        </div>
                    </div>

                    <!-- Budget Amounts Section -->
                    <div class="amount-section">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-money-bill-wave me-2"></i>Budget Amounts (PGK)
                        </h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="budgeted_amount" class="form-label">
                                        Budgeted Amount *
                                    </label>
                                    <div class="currency-input">
                                        <span class="currency-symbol">K</span>
                                        <input type="number" 
                                               class="form-control" 
                                               id="budgeted_amount" 
                                               name="budgeted_amount" 
                                               placeholder="0.00"
                                               step="0.01"
                                               min="0"
                                               required>
                                    </div>
                                    <div class="form-text">
                                        Total budgeted amount for this code.
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="allocated_amount" class="form-label">
                                        Allocated Amount *
                                    </label>
                                    <div class="currency-input">
                                        <span class="currency-symbol">K</span>
                                        <input type="number" 
                                               class="form-control" 
                                               id="allocated_amount" 
                                               name="allocated_amount" 
                                               placeholder="0.00"
                                               step="0.01"
                                               min="0"
                                               required>
                                    </div>
                                    <div class="form-text">
                                        Amount allocated for spending.
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="spent_amount" class="form-label">
                                        Spent Amount
                                    </label>
                                    <div class="currency-input">
                                        <span class="currency-symbol">K</span>
                                        <input type="number" 
                                               class="form-control" 
                                               id="spent_amount" 
                                               name="spent_amount" 
                                               placeholder="0.00"
                                               step="0.01"
                                               min="0"
                                               value="0">
                                    </div>
                                    <div class="form-text">
                                        Amount already spent (optional).
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="status" class="form-label">
                            <i class="fas fa-toggle-on me-2"></i>Status
                        </label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="Active" selected>Active</option>
                            <option value="Inactive">Inactive</option>
                            <option value="Suspended">Suspended</option>
                        </select>
                        <div class="form-text">
                            Set the status of this budget code.
                        </div>
                    </div>

                    <!-- Code Information -->
                    <div class="code-info">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-info-circle me-2"></i>Budget Code Guidelines
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p class="text-muted mb-2">
                                    <strong>Code Format Examples:</strong>
                                </p>
                                <div class="code-format mb-2">
                                    01.01.001 - Personnel Emoluments<br>
                                    01.02.001 - Goods and Services<br>
                                    01.03.001 - Capital Expenditure
                                </div>
                            </div>
                            <div class="col-md-6">
                                <p class="text-muted mb-2">
                                    <strong>Amount Guidelines:</strong>
                                </p>
                                <ul class="text-muted mb-0 small">
                                    <li>Allocated amount ≤ Budgeted amount</li>
                                    <li>Spent amount ≤ Allocated amount</li>
                                    <li>Use Papua New Guinea Kina (PGK)</li>
                                    <li>Enter amounts without commas</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex gap-3">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-save me-2"></i>Create Budget Code
                        </button>
                        <a href="<?= site_url('admin/budget-books/' . $budget_book['id'] . '/codes') ?>" class="btn btn-outline-secondary btn-lg">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('createBudgetCodeForm');
        const codeInput = document.getElementById('code');
        const nameInput = document.getElementById('name');
        const budgetedInput = document.getElementById('budgeted_amount');
        const allocatedInput = document.getElementById('allocated_amount');
        const spentInput = document.getElementById('spent_amount');
        
        // Form validation
        form.addEventListener('submit', function(e) {
            const code = codeInput.value.trim();
            const name = nameInput.value.trim();
            const budgeted = parseFloat(budgetedInput.value) || 0;
            const allocated = parseFloat(allocatedInput.value) || 0;
            const spent = parseFloat(spentInput.value) || 0;
            
            // Validate code format
            const codePattern = /^[0-9]{2}\.[0-9]{2}\.[0-9]{3}$/;
            if (!codePattern.test(code)) {
                e.preventDefault();
                alert('Budget code must follow the format XX.XX.XXX (e.g., 01.01.001)');
                codeInput.focus();
                return false;
            }
            
            if (name.length < 3) {
                e.preventDefault();
                alert('Budget code name must be at least 3 characters long.');
                nameInput.focus();
                return false;
            }
            
            if (budgeted <= 0) {
                e.preventDefault();
                alert('Budgeted amount must be greater than 0.');
                budgetedInput.focus();
                return false;
            }
            
            if (allocated > budgeted) {
                e.preventDefault();
                alert('Allocated amount cannot exceed budgeted amount.');
                allocatedInput.focus();
                return false;
            }
            
            if (spent > allocated) {
                e.preventDefault();
                alert('Spent amount cannot exceed allocated amount.');
                spentInput.focus();
                return false;
            }
            
            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';
            submitBtn.disabled = true;
        });
        
        // Auto-format code input
        codeInput.addEventListener('input', function() {
            let value = this.value.replace(/[^0-9]/g, '');
            if (value.length >= 2) {
                value = value.substring(0, 2) + '.' + value.substring(2);
            }
            if (value.length >= 6) {
                value = value.substring(0, 5) + '.' + value.substring(5, 8);
            }
            this.value = value;
        });
        
        // Validate allocated amount against budgeted
        allocatedInput.addEventListener('input', function() {
            const budgeted = parseFloat(budgetedInput.value) || 0;
            const allocated = parseFloat(this.value) || 0;
            
            if (allocated > budgeted && budgeted > 0) {
                this.setCustomValidity('Allocated amount cannot exceed budgeted amount');
            } else {
                this.setCustomValidity('');
            }
        });
        
        // Validate spent amount against allocated
        spentInput.addEventListener('input', function() {
            const allocated = parseFloat(allocatedInput.value) || 0;
            const spent = parseFloat(this.value) || 0;
            
            if (spent > allocated && allocated > 0) {
                this.setCustomValidity('Spent amount cannot exceed allocated amount');
            } else {
                this.setCustomValidity('');
            }
        });

        console.log('Create Budget Code form initialized successfully');
    });
</script>
<?= $this->endSection() ?>
