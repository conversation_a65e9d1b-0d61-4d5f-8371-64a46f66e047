<?php

namespace App\Controllers;

class AdminBudgetController extends BaseController
{
    /**
     * Display budget books list (GET request)
     *
     * @return string
     */
    public function index()
    {
        $data = [
            'page_title' => 'Budget Books Management',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Budget Books Management', 'url' => '']
            ],
            'budget_books' => $this->getDummyBudgetBooks(),
            'stats' => $this->getBudgetStats()
        ];

        return view('admin/admin_budget_index', $data);
    }

    /**
     * Show create budget book form (GET request)
     *
     * @return string
     */
    public function create()
    {
        $data = [
            'page_title' => 'Create Budget Book',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Budget Books Management', 'url' => site_url('admin/budget-books')],
                ['title' => 'Create Budget Book', 'url' => '']
            ]
        ];

        return view('admin/admin_budget_create', $data);
    }

    /**
     * Store new budget book (POST request)
     *
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function store()
    {
        // Mock response - in real implementation, this would save to database
        return redirect()->to(site_url('admin/budget-books'))
                        ->with('success', 'Budget Book created successfully!');
    }

    /**
     * Show edit budget book form (GET request)
     *
     * @param int $id
     * @return string
     */
    public function edit($id)
    {
        $budgetBooks = $this->getDummyBudgetBooks();
        $budgetBook = array_filter($budgetBooks, function($b) use ($id) {
            return $b['id'] == $id;
        });
        $budgetBook = reset($budgetBook);

        if (!$budgetBook) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Budget Book not found');
        }

        $data = [
            'page_title' => 'Edit Budget Book',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Budget Books Management', 'url' => site_url('admin/budget-books')],
                ['title' => 'Edit Budget Book', 'url' => '']
            ],
            'budget_book' => $budgetBook
        ];

        return view('admin/admin_budget_edit', $data);
    }

    /**
     * Update budget book (PUT request)
     *
     * @param int $id
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function update($id)
    {
        // Mock response - in real implementation, this would update the database
        return redirect()->to(site_url('admin/budget-books'))
                        ->with('success', 'Budget Book updated successfully!');
    }

    /**
     * Delete budget book (DELETE request)
     *
     * @param int $id
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function delete($id)
    {
        // Mock response - in real implementation, this would delete from database
        return redirect()->to(site_url('admin/budget-books'))
                        ->with('success', 'Budget Book deleted successfully!');
    }

    /**
     * Display budget codes for a specific budget book (GET request)
     *
     * @param int $budgetBookId
     * @return string
     */
    public function budgetCodes($budgetBookId)
    {
        $budgetBooks = $this->getDummyBudgetBooks();
        $budgetBook = array_filter($budgetBooks, function($b) use ($budgetBookId) {
            return $b['id'] == $budgetBookId;
        });
        $budgetBook = reset($budgetBook);

        if (!$budgetBook) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Budget Book not found');
        }

        $data = [
            'page_title' => 'Budget Codes - ' . $budgetBook['name'],
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Budget Books Management', 'url' => site_url('admin/budget-books')],
                ['title' => $budgetBook['name'], 'url' => '']
            ],
            'budget_book' => $budgetBook,
            'budget_codes' => $this->getDummyBudgetCodes($budgetBookId),
            'budget_summary' => $this->getBudgetSummary($budgetBookId)
        ];

        return view('admin/admin_budget_codes', $data);
    }

    /**
     * Show create budget code form (GET request)
     *
     * @param int $budgetBookId
     * @return string
     */
    public function createBudgetCode($budgetBookId)
    {
        $budgetBooks = $this->getDummyBudgetBooks();
        $budgetBook = array_filter($budgetBooks, function($b) use ($budgetBookId) {
            return $b['id'] == $budgetBookId;
        });
        $budgetBook = reset($budgetBook);

        if (!$budgetBook) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Budget Book not found');
        }

        $data = [
            'page_title' => 'Create Budget Code',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Budget Books Management', 'url' => site_url('admin/budget-books')],
                ['title' => $budgetBook['name'], 'url' => site_url('admin/budget-books/' . $budgetBookId . '/codes')],
                ['title' => 'Create Budget Code', 'url' => '']
            ],
            'budget_book' => $budgetBook,
            'budget_categories' => $this->getBudgetCategories()
        ];

        return view('admin/admin_budget_code_create', $data);
    }

    /**
     * Store new budget code (POST request)
     *
     * @param int $budgetBookId
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function storeBudgetCode($budgetBookId)
    {
        // Mock response - in real implementation, this would save to database
        return redirect()->to(site_url('admin/budget-books/' . $budgetBookId . '/codes'))
                        ->with('success', 'Budget Code created successfully!');
    }

    /**
     * Show edit budget code form (GET request)
     *
     * @param int $budgetBookId
     * @param int $codeId
     * @return string
     */
    public function editBudgetCode($budgetBookId, $codeId)
    {
        $budgetBooks = $this->getDummyBudgetBooks();
        $budgetBook = array_filter($budgetBooks, function($b) use ($budgetBookId) {
            return $b['id'] == $budgetBookId;
        });
        $budgetBook = reset($budgetBook);

        $budgetCodes = $this->getDummyBudgetCodes($budgetBookId);
        $budgetCode = array_filter($budgetCodes, function($c) use ($codeId) {
            return $c['id'] == $codeId;
        });
        $budgetCode = reset($budgetCode);

        if (!$budgetBook || !$budgetCode) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Budget Book or Budget Code not found');
        }

        $data = [
            'page_title' => 'Edit Budget Code',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Budget Books Management', 'url' => site_url('admin/budget-books')],
                ['title' => $budgetBook['name'], 'url' => site_url('admin/budget-books/' . $budgetBookId . '/codes')],
                ['title' => 'Edit Budget Code', 'url' => '']
            ],
            'budget_book' => $budgetBook,
            'budget_code' => $budgetCode,
            'budget_categories' => $this->getBudgetCategories()
        ];

        return view('admin/admin_budget_code_edit', $data);
    }

    /**
     * Update budget code (PUT request)
     *
     * @param int $budgetBookId
     * @param int $codeId
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function updateBudgetCode($budgetBookId, $codeId)
    {
        // Mock response - in real implementation, this would update the database
        return redirect()->to(site_url('admin/budget-books/' . $budgetBookId . '/codes'))
                        ->with('success', 'Budget Code updated successfully!');
    }

    /**
     * Delete budget code (DELETE request)
     *
     * @param int $budgetBookId
     * @param int $codeId
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function deleteBudgetCode($budgetBookId, $codeId)
    {
        // Mock response - in real implementation, this would delete from database
        return redirect()->to(site_url('admin/budget-books/' . $budgetBookId . '/codes'))
                        ->with('success', 'Budget Code deleted successfully!');
    }

    // ========================================
    // DUMMY DATA METHODS
    // ========================================

    /**
     * Get dummy budget books data
     *
     * @return array
     */
    private function getDummyBudgetBooks()
    {
        return [
            [
                'id' => 1,
                'name' => 'PNG National Budget 2024',
                'fiscal_year' => 2024,
                'description' => 'Papua New Guinea National Government Budget for 2024',
                'total_budget' => 15000000000, // 15 billion
                'status' => 'Active',
                'created_at' => '2024-01-15 10:30:00',
                'approved_at' => '2024-02-01 14:00:00',
                'approved_by' => 'Minister of Treasury'
            ],
            [
                'id' => 2,
                'name' => 'Department of Treasury Budget 2024',
                'fiscal_year' => 2024,
                'description' => 'Department of Treasury operational budget',
                'total_budget' => 250000000, // 250 million
                'status' => 'Active',
                'created_at' => '2024-01-20 09:15:00',
                'approved_at' => '2024-02-05 11:30:00',
                'approved_by' => 'Secretary for Treasury'
            ],
            [
                'id' => 3,
                'name' => 'Infrastructure Development Budget 2024',
                'fiscal_year' => 2024,
                'description' => 'Capital expenditure for infrastructure projects',
                'total_budget' => **********, // 3 billion
                'status' => 'Active',
                'created_at' => '2024-02-01 14:20:00',
                'approved_at' => '2024-02-15 16:45:00',
                'approved_by' => 'Minister of Works'
            ],
            [
                'id' => 4,
                'name' => 'Health Sector Budget 2024',
                'fiscal_year' => 2024,
                'description' => 'Healthcare services and infrastructure budget',
                'total_budget' => **********, // 1.2 billion
                'status' => 'Draft',
                'created_at' => '2024-02-10 11:00:00',
                'approved_at' => null,
                'approved_by' => null
            ],
            [
                'id' => 5,
                'name' => 'Education Budget 2024',
                'fiscal_year' => 2024,
                'description' => 'Education sector development and operations',
                'total_budget' => 800000000, // 800 million
                'status' => 'Under Review',
                'created_at' => '2024-02-15 13:30:00',
                'approved_at' => null,
                'approved_by' => null
            ]
        ];
    }

    /**
     * Get budget statistics
     *
     * @return array
     */
    private function getBudgetStats()
    {
        return [
            'total_budget_books' => 5,
            'active_budgets' => 3,
            'draft_budgets' => 1,
            'under_review' => 1,
            'total_allocated' => 20250000000 // 20.25 billion
        ];
    }

    /**
     * Get dummy budget codes data for a specific budget book
     *
     * @param int $budgetBookId
     * @return array
     */
    private function getDummyBudgetCodes($budgetBookId)
    {
        $allBudgetCodes = [
            // PNG National Budget 2024 codes
            [
                'id' => 1,
                'budget_book_id' => 1,
                'code' => '01.01.001',
                'name' => 'Personnel Emoluments - Salaries',
                'description' => 'Basic salaries for government employees',
                'category' => 'Personnel',
                'budgeted_amount' => 5000000000, // 5 billion
                'allocated_amount' => 4800000000,
                'spent_amount' => **********,
                'status' => 'Active',
                'created_at' => '2024-01-15 11:00:00'
            ],
            [
                'id' => 2,
                'budget_book_id' => 1,
                'code' => '01.02.001',
                'name' => 'Goods and Services - Office Supplies',
                'description' => 'Office supplies and consumables',
                'category' => 'Goods and Services',
                'budgeted_amount' => 150000000, // 150 million
                'allocated_amount' => 150000000,
                'spent_amount' => 45000000,
                'status' => 'Active',
                'created_at' => '2024-01-15 11:15:00'
            ],
            [
                'id' => 3,
                'budget_book_id' => 1,
                'code' => '01.03.001',
                'name' => 'Capital Expenditure - Infrastructure',
                'description' => 'Major infrastructure development projects',
                'category' => 'Capital',
                'budgeted_amount' => 8000000000, // 8 billion
                'allocated_amount' => 7500000000,
                'spent_amount' => 2000000000,
                'status' => 'Active',
                'created_at' => '2024-01-15 11:30:00'
            ],
            [
                'id' => 4,
                'budget_book_id' => 1,
                'code' => '01.04.001',
                'name' => 'Debt Service - Interest Payments',
                'description' => 'Interest payments on government debt',
                'category' => 'Debt Service',
                'budgeted_amount' => 1850000000, // 1.85 billion
                'allocated_amount' => 1850000000,
                'spent_amount' => 462500000,
                'status' => 'Active',
                'created_at' => '2024-01-15 11:45:00'
            ],
            // Department of Treasury Budget codes
            [
                'id' => 5,
                'budget_book_id' => 2,
                'code' => '02.01.001',
                'name' => 'Personnel - Treasury Staff',
                'description' => 'Salaries and benefits for Treasury personnel',
                'category' => 'Personnel',
                'budgeted_amount' => 120000000, // 120 million
                'allocated_amount' => 120000000,
                'spent_amount' => 30000000,
                'status' => 'Active',
                'created_at' => '2024-01-20 10:00:00'
            ],
            [
                'id' => 6,
                'budget_book_id' => 2,
                'code' => '02.02.001',
                'name' => 'Operations - Financial Systems',
                'description' => 'Financial management systems and operations',
                'category' => 'Operations',
                'budgeted_amount' => 80000000, // 80 million
                'allocated_amount' => 75000000,
                'spent_amount' => 20000000,
                'status' => 'Active',
                'created_at' => '2024-01-20 10:15:00'
            ],
            [
                'id' => 7,
                'budget_book_id' => 2,
                'code' => '02.03.001',
                'name' => 'Training and Development',
                'description' => 'Staff capacity building and training programs',
                'category' => 'Development',
                'budgeted_amount' => 50000000, // 50 million
                'allocated_amount' => 50000000,
                'spent_amount' => 8000000,
                'status' => 'Active',
                'created_at' => '2024-01-20 10:30:00'
            ],
            // Infrastructure Development Budget codes
            [
                'id' => 8,
                'budget_book_id' => 3,
                'code' => '03.01.001',
                'name' => 'Roads and Highways',
                'description' => 'Construction and maintenance of road networks',
                'category' => 'Infrastructure',
                'budgeted_amount' => 1500000000, // 1.5 billion
                'allocated_amount' => 1400000000,
                'spent_amount' => 350000000,
                'status' => 'Active',
                'created_at' => '2024-02-01 15:00:00'
            ],
            [
                'id' => 9,
                'budget_book_id' => 3,
                'code' => '03.02.001',
                'name' => 'Water and Sanitation',
                'description' => 'Water supply and sanitation infrastructure',
                'category' => 'Infrastructure',
                'budgeted_amount' => 800000000, // 800 million
                'allocated_amount' => 750000000,
                'spent_amount' => 150000000,
                'status' => 'Active',
                'created_at' => '2024-02-01 15:15:00'
            ],
            [
                'id' => 10,
                'budget_book_id' => 3,
                'code' => '03.03.001',
                'name' => 'Energy Infrastructure',
                'description' => 'Power generation and distribution projects',
                'category' => 'Infrastructure',
                'budgeted_amount' => 700000000, // 700 million
                'allocated_amount' => 650000000,
                'spent_amount' => 100000000,
                'status' => 'Active',
                'created_at' => '2024-02-01 15:30:00'
            ]
        ];

        return array_filter($allBudgetCodes, function($code) use ($budgetBookId) {
            return $code['budget_book_id'] == $budgetBookId;
        });
    }

    /**
     * Get budget summary for a specific budget book
     *
     * @param int $budgetBookId
     * @return array
     */
    private function getBudgetSummary($budgetBookId)
    {
        $codes = $this->getDummyBudgetCodes($budgetBookId);

        $totalBudgeted = array_sum(array_column($codes, 'budgeted_amount'));
        $totalAllocated = array_sum(array_column($codes, 'allocated_amount'));
        $totalSpent = array_sum(array_column($codes, 'spent_amount'));

        return [
            'total_codes' => count($codes),
            'total_budgeted' => $totalBudgeted,
            'total_allocated' => $totalAllocated,
            'total_spent' => $totalSpent,
            'remaining_budget' => $totalAllocated - $totalSpent,
            'utilization_rate' => $totalAllocated > 0 ? round(($totalSpent / $totalAllocated) * 100, 2) : 0
        ];
    }

    /**
     * Get budget categories
     *
     * @return array
     */
    private function getBudgetCategories()
    {
        return [
            'Personnel' => 'Personnel Emoluments',
            'Goods and Services' => 'Goods and Services',
            'Capital' => 'Capital Expenditure',
            'Debt Service' => 'Debt Service',
            'Operations' => 'Operational Expenses',
            'Development' => 'Development Programs',
            'Infrastructure' => 'Infrastructure Projects',
            'Social Services' => 'Social Services',
            'Security' => 'Security and Defense',
            'Other' => 'Other Expenses'
        ];
    }
}