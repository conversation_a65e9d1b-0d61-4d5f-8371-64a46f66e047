<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .admin-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.12);
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .table th {
        background-color: #f8f9fa;
        border-top: none;
        color: #800000;
        font-weight: 600;
    }

    .btn-action {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        margin: 0 0.125rem;
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .metric-card {
        text-align: center;
        padding: 1.5rem;
        border-radius: 10px;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    }

    .metric-value {
        font-size: 2rem;
        font-weight: bold;
        color: #800000;
    }

    .metric-label {
        color: #6c757d;
        font-size: 0.875rem;
        margin-top: 0.5rem;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-balance-scale me-2"></i>Legislation Management
                        </h3>
                        <p class="text-muted mb-0">
                            Create and manage legislation documents that define mandatory activities for the organization.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="btn-group">
                            <a href="<?= site_url('admin/legislated-activities') ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Legislated Activities
                            </a>
                            <a href="<?= site_url('admin/legislation/create') ?>" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>Create Legislation
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Overview -->
    <div class="row g-4 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="metric-card">
                <div class="metric-value"><?= $stats['total_legislation'] ?></div>
                <div class="metric-label">Total Legislation</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="metric-card">
                <div class="metric-value"><?= $stats['total_activities'] ?></div>
                <div class="metric-label">Total Activities</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="metric-card">
                <div class="metric-value"><?= $stats['active_legislation'] ?></div>
                <div class="metric-label">Active Legislation</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="metric-card">
                <div class="metric-value"><?= $stats['avg_activities_per_legislation'] ?></div>
                <div class="metric-label">Avg Activities per Law</div>
            </div>
        </div>
    </div>

    <!-- Legislation Table View -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="fw-bold mb-0" style="color: #800000;">
                        <i class="fas fa-list me-2"></i>Legislation List
                    </h5>
                    <div class="d-flex gap-2">
                        <input type="text" class="form-control form-control-sm" placeholder="Search legislation..." style="width: 200px;">
                        <button class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Legislation Name</th>
                                <th>Reference Number</th>
                                <th>Date Enacted</th>
                                <th>Status</th>
                                <th>Activities</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($legislation as $law): ?>
                            <tr>
                                <td>
                                    <div>
                                        <strong><?= esc($law['name']) ?></strong>
                                        <br><small class="text-muted"><?= esc(substr($law['description'], 0, 80)) ?>...</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-primary"><?= esc($law['reference_number']) ?></span>
                                </td>
                                <td><?= date('M d, Y', strtotime($law['date_enacted'])) ?></td>
                                <td>
                                    <span class="badge bg-success status-badge"><?= esc($law['status']) ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?= $law['activities_count'] ?> Activities</span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= site_url('admin/legislation/' . $law['id'] . '/activities') ?>"
                                           class="btn btn-outline-primary btn-action"
                                           title="View Activities">
                                            <i class="fas fa-tasks"></i>
                                        </a>
                                        <a href="<?= site_url('admin/legislation/' . $law['id'] . '/edit') ?>"
                                           class="btn btn-outline-secondary btn-action"
                                           title="Edit Legislation">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form method="post" action="<?= site_url('admin/legislation/' . $law['id']) ?>" style="display: inline;">
                                            <?= csrf_field() ?>
                                            <input type="hidden" name="_method" value="DELETE">
                                            <button type="submit"
                                                    class="btn btn-outline-danger btn-action"
                                                    title="Delete Legislation"
                                                    onclick="return confirm('Are you sure you want to delete this legislation?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted">
                        Showing <?= count($legislation) ?> of <?= count($legislation) ?> legislation
                    </div>
                    <nav aria-label="Legislation pagination">
                        <ul class="pagination pagination-sm mb-0">
                            <li class="page-item disabled">
                                <span class="page-link">Previous</span>
                            </li>
                            <li class="page-item active">
                                <span class="page-link">1</span>
                            </li>
                            <li class="page-item disabled">
                                <span class="page-link">Next</span>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <h5 class="fw-bold mb-3" style="color: #800000;">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h5>
                <div class="row g-3">
                    <div class="col-lg-3 col-md-6">
                        <a href="<?= site_url('admin/legislation/create') ?>" class="btn btn-outline-primary w-100 py-3">
                            <i class="fas fa-plus-circle me-2"></i>Create Legislation
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <button class="btn btn-outline-success w-100 py-3">
                            <i class="fas fa-file-import me-2"></i>Import Legislation
                        </button>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <button class="btn btn-outline-info w-100 py-3">
                            <i class="fas fa-chart-bar me-2"></i>View Reports
                        </button>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <button class="btn btn-outline-warning w-100 py-3">
                            <i class="fas fa-download me-2"></i>Export Data
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Search functionality
        document.querySelector('input[placeholder="Search legislation..."]').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const tableRows = document.querySelectorAll('tbody tr');

            tableRows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });

        console.log('Legislation Management initialized successfully');
    });
</script>
<?= $this->endSection() ?>
