<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .admin-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.12);
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .table th {
        background-color: #f8f9fa;
        border-top: none;
        color: #800000;
        font-weight: 600;
    }

    .btn-action {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        margin: 0 0.125rem;
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .progress-bar-custom {
        height: 8px;
        border-radius: 4px;
    }

    .stats-card {
        background: linear-gradient(135deg, #800000 0%, #28a745 100%);
        color: white;
        border-radius: 10px;
        padding: 1rem;
        text-align: center;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-clipboard-list me-2"></i>Workplan Management
                        </h3>
                        <p class="text-muted mb-0">
                            Create and manage workplans with activities. Link activities to plans, budget codes, assets, and officers.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="btn-group">
                            <a href="<?= site_url('dashboard') ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                            <a href="<?= site_url('workplan-management/create') ?>" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>Create New Workplan
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="stats-card">
                <i class="fas fa-clipboard-list fs-2 mb-2"></i>
                <h4 class="fw-bold"><?= $stats['total_workplans'] ?></h4>
                <small>Total Workplans</small>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="stats-card">
                <i class="fas fa-play-circle fs-2 mb-2"></i>
                <h4 class="fw-bold"><?= $stats['active_workplans'] ?></h4>
                <small>Active Workplans</small>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="stats-card">
                <i class="fas fa-check-circle fs-2 mb-2"></i>
                <h4 class="fw-bold"><?= $stats['completed_workplans'] ?></h4>
                <small>Completed</small>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="stats-card">
                <i class="fas fa-tasks fs-2 mb-2"></i>
                <h4 class="fw-bold"><?= $stats['total_activities'] ?></h4>
                <small>Total Activities</small>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="stats-card">
                <i class="fas fa-chart-line fs-2 mb-2"></i>
                <h4 class="fw-bold"><?= $stats['completion_rate'] ?>%</h4>
                <small>Completion Rate</small>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="stats-card">
                <i class="fas fa-calendar-check fs-2 mb-2"></i>
                <h4 class="fw-bold"><?= $stats['completed_activities'] ?></h4>
                <small>Completed Activities</small>
            </div>
        </div>
    </div>

    <!-- Workplans Table -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="fw-bold mb-0" style="color: #800000;">
                        <i class="fas fa-list me-2"></i>Workplans List
                    </h5>
                    <div class="d-flex gap-2">
                        <input type="text" class="form-control form-control-sm" placeholder="Search workplans..." style="width: 200px;">
                        <button class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Position</th>
                                <th>Supervisor</th>
                                <th>Fiscal Year</th>
                                <th>Activities</th>
                                <th>Progress</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($workplans as $workplan): ?>
                            <tr>
                                <td>
                                    <div>
                                        <strong><?= esc($workplan['position_name']) ?></strong>
                                    </div>
                                </td>
                                <td><?= esc($workplan['supervisor_name']) ?></td>
                                <td>
                                    <span class="badge bg-info"><?= esc($workplan['fiscal_year']) ?></span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="me-2"><?= $workplan['completed_activities'] ?>/<?= $workplan['activities_count'] ?></span>
                                        <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities') ?>" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-tasks me-1"></i>Manage
                                        </a>
                                    </div>
                                </td>
                                <td>
                                    <?php 
                                    $progress = $workplan['activities_count'] > 0 ? 
                                        round(($workplan['completed_activities'] / $workplan['activities_count']) * 100) : 0;
                                    ?>
                                    <div class="progress progress-bar-custom">
                                        <div class="progress-bar bg-success" style="width: <?= $progress ?>%"></div>
                                    </div>
                                    <small class="text-muted"><?= $progress ?>%</small>
                                </td>
                                <td>
                                    <?php if ($workplan['status'] === 'Active'): ?>
                                        <span class="badge bg-success status-badge">Active</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary status-badge">Completed</span>
                                    <?php endif; ?>
                                </td>
                                <td><?= date('M d, Y', strtotime($workplan['created_at'])) ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities') ?>"
                                           class="btn btn-outline-info btn-action"
                                           title="Manage Activities">
                                            <i class="fas fa-tasks"></i>
                                        </a>
                                        <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/edit') ?>"
                                           class="btn btn-outline-primary btn-action"
                                           title="Edit Workplan">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button"
                                                class="btn btn-outline-danger btn-action"
                                                title="Delete Workplan"
                                                onclick="confirmDelete(<?= $workplan['id'] ?>, '<?= esc($workplan['position_name']) ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted">
                        Showing <?= count($workplans) ?> of <?= count($workplans) ?> workplans
                    </div>
                    <nav aria-label="Workplans pagination">
                        <ul class="pagination pagination-sm mb-0">
                            <li class="page-item disabled">
                                <span class="page-link">Previous</span>
                            </li>
                            <li class="page-item active">
                                <span class="page-link">1</span>
                            </li>
                            <li class="page-item disabled">
                                <span class="page-link">Next</span>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="d-flex gap-2">
                <a href="<?= site_url('dashboard') ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                </a>
                <a href="<?= site_url('workplan-management/create') ?>" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>Create New Workplan
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete workplan for <strong id="deleteWorkplanName"></strong>?</p>
                <p class="text-danger"><small>This action cannot be undone and will also delete all associated activities.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete Workplan</button>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    let deleteWorkplanId = null;

    function confirmDelete(workplanId, positionName) {
        deleteWorkplanId = workplanId;
        document.getElementById('deleteWorkplanName').textContent = positionName;
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        deleteModal.show();
    }

    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
        if (deleteWorkplanId) {
            // Create a form to submit DELETE request
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `<?= site_url('workplan-management/') ?>${deleteWorkplanId}`;
            
            // Add method override for DELETE
            const methodInput = document.createElement('input');
            methodInput.type = 'hidden';
            methodInput.name = '_method';
            methodInput.value = 'DELETE';
            form.appendChild(methodInput);
            
            document.body.appendChild(form);
            form.submit();
        }
    });

    // Search functionality
    document.querySelector('input[placeholder="Search workplans..."]').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const tableRows = document.querySelectorAll('tbody tr');

        tableRows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });

    console.log('Workplan Management initialized successfully');
</script>
<?= $this->endSection() ?>
