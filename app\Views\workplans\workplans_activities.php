<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .admin-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.12);
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .table th {
        background-color: #f8f9fa;
        border-top: none;
        color: #800000;
        font-weight: 600;
    }

    .btn-action {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        margin: 0 0.125rem;
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .type-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
    }

    .workplan-info {
        background: linear-gradient(135deg, #800000 0%, #28a745 100%);
        color: white;
        border-radius: 10px;
        padding: 1.5rem;
    }

    .stats-card {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        text-align: center;
        border-left: 4px solid #28a745;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Workplan Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="workplan-info">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2">
                            <i class="fas fa-clipboard-list me-2"></i>Workplan Activities
                        </h3>
                        <h5 class="mb-1"><?= esc($workplan['position_name']) ?> - Fiscal Year <?= esc($workplan['fiscal_year']) ?></h5>
                        <p class="mb-0 opacity-75">
                            Supervisor: <?= esc($workplan['supervisor_name']) ?> |
                            Status: <?= esc($workplan['status']) ?> |
                            Activities: <?= $workplan['completed_activities'] ?>/<?= $workplan['activities_count'] ?> completed
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="btn-group">
                            <a href="<?= site_url('workplan-management') ?>" class="btn btn-outline-light">
                                <i class="fas fa-arrow-left me-2"></i>Back to Workplans
                            </a>
                            <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities/create') ?>" class="btn btn-light">
                                <i class="fas fa-plus me-2"></i>Add Activity
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="stats-card">
                <i class="fas fa-tasks fs-2 mb-2 text-primary"></i>
                <h4 class="fw-bold"><?= $stats['total_activities'] ?></h4>
                <small class="text-muted">Total Activities</small>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-card">
                <i class="fas fa-check-circle fs-2 mb-2 text-success"></i>
                <h4 class="fw-bold"><?= $stats['completed_activities'] ?></h4>
                <small class="text-muted">Completed</small>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-card">
                <i class="fas fa-play-circle fs-2 mb-2 text-warning"></i>
                <h4 class="fw-bold"><?= $stats['in_progress_activities'] ?></h4>
                <small class="text-muted">In Progress</small>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-card">
                <i class="fas fa-clock fs-2 mb-2 text-info"></i>
                <h4 class="fw-bold"><?= $stats['pending_activities'] ?></h4>
                <small class="text-muted">Pending</small>
            </div>
        </div>
    </div>

    <!-- Activities Table -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="fw-bold mb-0" style="color: #800000;">
                        <i class="fas fa-list me-2"></i>Activities List
                    </h5>
                    <div class="d-flex gap-2">
                        <input type="text" class="form-control form-control-sm" placeholder="Search activities..." style="width: 200px;">
                        <button class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Activity Name</th>
                                <th>Type</th>
                                <th>Assigned To</th>
                                <th>Duration</th>
                                <th>Budget Code</th>
                                <th>Linked Plan</th>
                                <th>Resources</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($activities as $activity): ?>
                            <tr>
                                <td>
                                    <div>
                                        <strong><?= esc($activity['name']) ?></strong>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                    $typeClass = '';
                                    switch($activity['type']) {
                                        case 'Recurrent': $typeClass = 'bg-primary'; break;
                                        case 'Project': $typeClass = 'bg-warning'; break;
                                        case 'Legislated': $typeClass = 'bg-info'; break;
                                        default: $typeClass = 'bg-secondary';
                                    }
                                    ?>
                                    <span class="badge <?= $typeClass ?> type-badge"><?= esc($activity['type']) ?></span>
                                </td>
                                <td><?= esc($activity['position_name']) ?></td>
                                <td>
                                    <small>
                                        <?= date('M d, Y', strtotime($activity['start_date'])) ?><br>
                                        to <?= date('M d, Y', strtotime($activity['end_date'])) ?>
                                    </small>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?= esc($activity['budget_code']) ?></span>
                                </td>
                                <td>
                                    <small><?= esc($activity['linked_plan']) ?></small>
                                </td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <span class="badge bg-light text-dark" title="Assets">
                                            <i class="fas fa-box me-1"></i><?= $activity['assets_count'] ?>
                                        </span>
                                        <span class="badge bg-light text-dark" title="Officers">
                                            <i class="fas fa-users me-1"></i><?= $activity['officers_count'] ?>
                                        </span>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                    $statusClass = '';
                                    switch($activity['status']) {
                                        case 'Completed': $statusClass = 'bg-success'; break;
                                        case 'In Progress': $statusClass = 'bg-warning'; break;
                                        case 'Pending': $statusClass = 'bg-secondary'; break;
                                        default: $statusClass = 'bg-secondary';
                                    }
                                    ?>
                                    <span class="badge <?= $statusClass ?> status-badge"><?= esc($activity['status']) ?></span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities/' . $activity['id'] . '/targets') ?>"
                                           class="btn btn-outline-warning btn-action"
                                           title="Manage Targets">
                                            <i class="fas fa-bullseye"></i>
                                        </a>
                                        <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities/' . $activity['id'] . '/link') ?>"
                                           class="btn btn-outline-success btn-action"
                                           title="Link Resources">
                                            <i class="fas fa-link"></i>
                                        </a>
                                        <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities/' . $activity['id'] . '/edit') ?>"
                                           class="btn btn-outline-primary btn-action"
                                           title="Edit Activity">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button"
                                                class="btn btn-outline-danger btn-action"
                                                title="Delete Activity"
                                                onclick="confirmDelete(<?= $activity['id'] ?>, '<?= esc($activity['name']) ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted">
                        Showing <?= count($activities) ?> of <?= count($activities) ?> activities
                    </div>
                    <nav aria-label="Activities pagination">
                        <ul class="pagination pagination-sm mb-0">
                            <li class="page-item disabled">
                                <span class="page-link">Previous</span>
                            </li>
                            <li class="page-item active">
                                <span class="page-link">1</span>
                            </li>
                            <li class="page-item disabled">
                                <span class="page-link">Next</span>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="d-flex gap-2">
                <a href="<?= site_url('workplan-management') ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Workplans
                </a>
                <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities/create') ?>" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>Add New Activity
                </a>
                <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/edit') ?>" class="btn btn-outline-primary">
                    <i class="fas fa-edit me-2"></i>Edit Workplan
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete activity <strong id="deleteActivityName"></strong>?</p>
                <p class="text-danger"><small>This action cannot be undone and will also remove all linked resources.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete Activity</button>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    let deleteActivityId = null;

    function confirmDelete(activityId, activityName) {
        deleteActivityId = activityId;
        document.getElementById('deleteActivityName').textContent = activityName;
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        deleteModal.show();
    }

    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
        if (deleteActivityId) {
            // Create a form to submit DELETE request
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `<?= site_url('workplan-management/' . $workplan['id'] . '/activities/') ?>${deleteActivityId}`;

            // Add method override for DELETE
            const methodInput = document.createElement('input');
            methodInput.type = 'hidden';
            methodInput.name = '_method';
            methodInput.value = 'DELETE';
            form.appendChild(methodInput);

            document.body.appendChild(form);
            form.submit();
        }
    });

    // Search functionality
    document.querySelector('input[placeholder="Search activities..."]').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const tableRows = document.querySelectorAll('tbody tr');

        tableRows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });

    console.log('Workplan Activities initialized successfully');
</script>
<?= $this->endSection() ?>
