I wanted to create a progress monitoring system that monitors the human resource, organizational plans, budget(finance) and usage of assets. All linking to Workplans. Combination of several workplans creates the Annual Activity Plans (AAP)

This is how the above will be linked to a workplan.
- A workplan is developed by an officer (Human Resource) for himself for the whole year. In the workplan, that officer will have two types of activities: Activities (Recurring Activities - Recurring every year) and Projects (one off activities or activities that doesn't occur every year /annually)
- Legislated activities should come under Activities (Legislated activities will be listed by the administrator and assigned to the group) these activities are a must to be linked to a workplan

This is how the workplan will be linked: the workplan is developed, key activities are created. Each workplan is assigned to a supervisor, each activities are assigned to an officer. 

The activities in the workplan will be listed in the following categories: 
 - Project Activities: (projects will be linked to development plan programs and indicators) each project will have phases and milestones). In the workplan, only milestones are gonna be entered.
 - Recurrent Activities: they will be pre-listed in the group (these activities will be tied to a budget code item, link to corporate plan)
 - Legislated Activities: these will be pre-listed by the administrator not the group supervisor or manager 

Each workplan will link to one or more budget book code item.
The budget book is listed in codes and items and amount budgeted. So this is the structure of Budget book: Book (1 fiscal year) -> : - A. Revenue Codes (list all revenue codes and items and amounts), B. Expenditure Codes (list all the codes and items and amounts) -> assign each code to a group or multiple group) (list in a simple crud table: fields: No., Code, Item, amount, link groups, remarks)

This is the structure of Corporate Plan: Plan -> KRA -> KPI -> Indicators (indicators are linked to group or multiple groups)
This is the structure of Development plan: DevPlan -> Programs -> Projects (projects are linked to group or multiple groups)
This is the structure of Legislated Activities: Legislation -> Activities (activities are linked to 1 or more groups)
This is the structure of group centered Recurrent Activities: inside group: Activity Groups -> Activities 
This is the workplan structure: Workplan -> Activity (assign activities to officer)

So this is where workplan comes in after the KPIs and KRAs and Legislated Activities are linked to the groups:
- The workplan is created based on these linked activities 

These activities are used to create Workplan activities. Eg. Project: when a project assigned to a group. That group will create milestones of the project. These milestones will then be linked to create a workplan activity linked to that milestone. One milestone can have multiple workplan activities linked to it.
Eg. Recurrent Activities: a Recurrent activity inside a group will be used to create one or multiple workplan activities related to that Recurrent activity 
Eg. Legislated Activities: A Legislated Activity linked to a group, one of more workplan activities will be created based on that one Legislated Activity

- multiple of these activities can be inside a workplan
- each workplan activity will be linked to a budget code item (optional). That same workplan activity will be linked to a Project activity or Recurrent Activity or Project Activity. And that workplan activity will have assets to carry out it's work (optional)
- NB: workplan activities are different from other mentioned activities. They are more like assigned tasks with more details. 

- there will be a feature to raise claims for funding. Each claim must be tied to one or more workplan activity. 
- when an activity is selected. An amount shall be  required to be entered if the activity is linked to a budget code item, if not then the field be disabled and not required. This also applies to multiple activities added for a single claim.
- the total sum of the amount will be displayed in the FF3 and FF4 forms. The forms will be automatically generated into PDF format and saved for download. If regenerated, the form will show revised versions number according to number of times regenerated.
- Along with the FF3 and FF4 Forms and Alignment Sheet will be generated (this Alignment sheet will display how these workplan activities or activity linked to the financial claim is linked to the overall organizational plans and linked to budget. The plans be it, corporate plan or development plan and the indicators it will address.
- It will also show amount already expanded and the amount balance due from the budgeted amount and amount posted by budget people (if available or not 0)
- For an officer to request a claim, the officer will create budget table of each financial claim request. The workplan activity budget will have a code which will be sent to the fund manager, which will be assigned, upon fund manager approval it will be listed on the workflow to generate FF3 & FF4 and Alignment Sheet for ARO to raise the claim
- for each activities, the officer assigned will create report, and submit acquitals against each financial claim

Users management 
- user management will have full name, email address, file number, gender, dobirth, date joined, id photo, contact details, remarks 
- 

User groups 
- Create user groups. Inside the user group, create the position structure. 
- the positions will have position no, position name, grade and reporting to, position type (public Servant or casual) the position will also have is fund manager position, is supervisor, is group admin
- Group Admin will create the group Recurrent Activities   
- the groups will have child parent structure 
- workplans will be created and assigned to positions, not the person. 
- all the groups and positions will be created under a structure - structure has to be created and only one structure has to be activated at one time. 


Menu Features under Administrator settings will be:
- Users management (CRUD users)
- Structure Management (Structure CRUD -> Groups CRUD -> Positions CRUD)
- Appointments (assign employees to positions, can import from csv file)
- Plans Management (Plans (type: development/corporate) CRUD -> 
- if development: Plans -> Programs -> Projects
- if corporate: Plans -> KRAs -> KPIs 
Budget Book Management: Budget Book (CRUD) -> code item, amount, type (expenditure/revenue)
- create each budget book be created for each fiscal year. 
- Legislated Activities -> Legislation CRUD -> Activities CRUD

The buttons for officers will show the following: 
- standard buttons: My Workplan Activities, my profile, my aquitals, my report
- if Group admin: group settings plus standard buttons 
- if supervisor: Manage Workplans plus standard buttons
- if ARO: Financial Claims plus standard buttons
- if Fund Manager: Financial Claims, Claims Workflow (pending approvals) plus standard buttons
- if Administrator: Administrator plus standard buttons






System information 
- the system will be built using codeigniter 4 with some specific form submissions be standard codeigniter 4 CRUD and others Ajax submissions as specified. 
- the system will be built using RESTful approach 
- the system will have interface with only buttons, no sidebar. The interface frontend will use bootstrap 5 frontend framework 
- the backend will be mysql db.
- The development environment will be on xampp server 
- the name of this system is: IPMS (Integrated Progress Monitoring System)
- The interface is aimed to give a mobile app feeling, instead of a traditional web app feeling. So the interface doesn't need to have a sidebar navigation menu or the top navigation menu. All the menus should be treated as buttons. And they should be displayed within the main content area of the system. 




 