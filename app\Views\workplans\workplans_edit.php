<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .admin-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.12);
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .form-label {
        font-weight: 600;
        color: #800000;
    }

    .form-control:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    .form-select:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    .btn-success {
        background-color: #28a745;
        border-color: #28a745;
    }

    .btn-success:hover {
        background-color: #218838;
        border-color: #1e7e34;
    }

    .required {
        color: #dc3545;
    }

    .info-badge {
        background-color: #e9ecef;
        color: #495057;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-size: 0.875rem;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-edit me-2"></i>Edit Workplan
                        </h3>
                        <p class="text-muted mb-0">
                            Update workplan information for <?= esc($workplan['position_name']) ?> - <?= esc($workplan['fiscal_year']) ?>
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="btn-group">
                            <a href="<?= site_url('workplan-management') ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Workplans
                            </a>
                            <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities') ?>" class="btn btn-outline-info">
                                <i class="fas fa-tasks me-2"></i>Manage Activities
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <h6 class="fw-bold text-info mb-3">
                    <i class="fas fa-info-circle me-2"></i>Current Workplan Information
                </h6>
                <div class="row g-3">
                    <div class="col-md-3">
                        <div class="info-badge">
                            <strong>Position:</strong><br>
                            <?= esc($workplan['position_name']) ?>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-badge">
                            <strong>Supervisor:</strong><br>
                            <?= esc($workplan['supervisor_name']) ?>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="info-badge">
                            <strong>Fiscal Year:</strong><br>
                            <?= esc($workplan['fiscal_year']) ?>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="info-badge">
                            <strong>Activities:</strong><br>
                            <?= $workplan['activities_count'] ?> total
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="info-badge">
                            <strong>Status:</strong><br>
                            <span class="badge bg-<?= $workplan['status'] === 'Active' ? 'success' : 'secondary' ?>"><?= esc($workplan['status']) ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <h5 class="fw-bold mb-4" style="color: #800000;">
                    <i class="fas fa-clipboard-list me-2"></i>Update Workplan Information
                </h5>

                <form action="<?= site_url('workplan-management/' . $workplan['id']) ?>" method="POST">
                    <?= csrf_field() ?>
                    <input type="hidden" name="_method" value="PUT">
                    
                    <div class="row g-4">
                        <!-- Position Selection -->
                        <div class="col-md-6">
                            <label for="position_id" class="form-label">
                                Position <span class="required">*</span>
                            </label>
                            <select class="form-select" id="position_id" name="position_id" required>
                                <option value="">Select Position</option>
                                <?php foreach ($positions as $position): ?>
                                    <option value="<?= $position['id'] ?>" 
                                            <?= $position['name'] === $workplan['position_name'] ? 'selected' : '' ?>
                                            data-group="<?= esc($position['group']) ?>">
                                        <?= esc($position['name']) ?> (<?= esc($position['group']) ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Select the position for which this workplan is being created.</div>
                        </div>

                        <!-- Supervisor Position Selection -->
                        <div class="col-md-6">
                            <label for="supervisor_position_id" class="form-label">
                                Supervisor Position <span class="required">*</span>
                            </label>
                            <select class="form-select" id="supervisor_position_id" name="supervisor_position_id" required>
                                <option value="">Select Supervisor Position</option>
                                <?php foreach ($positions as $position): ?>
                                    <option value="<?= $position['id'] ?>"
                                            <?= $position['name'] === $workplan['supervisor_name'] ? 'selected' : '' ?>>
                                        <?= esc($position['name']) ?> (<?= esc($position['group']) ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Select the supervisor position for this workplan.</div>
                        </div>

                        <!-- Fiscal Year -->
                        <div class="col-md-6">
                            <label for="fiscal_year" class="form-label">
                                Fiscal Year <span class="required">*</span>
                            </label>
                            <select class="form-select" id="fiscal_year" name="fiscal_year" required>
                                <option value="">Select Fiscal Year</option>
                                <?php foreach ($fiscal_years as $year): ?>
                                    <option value="<?= $year ?>" <?= $year == $workplan['fiscal_year'] ? 'selected' : '' ?>>
                                        <?= $year ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Select the fiscal year for this workplan.</div>
                        </div>

                        <!-- Status -->
                        <div class="col-md-6">
                            <label for="status" class="form-label">
                                Status <span class="required">*</span>
                            </label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="Active" <?= $workplan['status'] === 'Active' ? 'selected' : '' ?>>Active</option>
                                <option value="Draft" <?= $workplan['status'] === 'Draft' ? 'selected' : '' ?>>Draft</option>
                                <option value="Completed" <?= $workplan['status'] === 'Completed' ? 'selected' : '' ?>>Completed</option>
                            </select>
                            <div class="form-text">Update the status of the workplan.</div>
                        </div>

                        <!-- Description -->
                        <div class="col-12">
                            <label for="description" class="form-label">
                                Description
                            </label>
                            <textarea class="form-control" id="description" name="description" rows="4" 
                                      placeholder="Enter workplan description or objectives...">Workplan for <?= esc($workplan['position_name']) ?> covering fiscal year <?= esc($workplan['fiscal_year']) ?>. This workplan includes various activities and projects to be completed during the specified period.</textarea>
                            <div class="form-text">Optional description or objectives for this workplan.</div>
                        </div>

                        <!-- Remarks -->
                        <div class="col-12">
                            <label for="remarks" class="form-label">
                                Remarks
                            </label>
                            <textarea class="form-control" id="remarks" name="remarks" rows="3" 
                                      placeholder="Enter any additional remarks or notes...">Last updated on <?= date('M d, Y', strtotime($workplan['updated_at'])) ?>. Contains <?= $workplan['activities_count'] ?> activities with <?= $workplan['completed_activities'] ?> completed.</textarea>
                            <div class="form-text">Optional remarks or additional notes.</div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-2"></i>Update Workplan
                                </button>
                                <a href="<?= site_url('workplan-management') ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                                <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities') ?>" class="btn btn-outline-info">
                                    <i class="fas fa-tasks me-2"></i>Manage Activities
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Form validation
        const form = document.querySelector('form');
        const positionSelect = document.getElementById('position_id');
        const supervisorSelect = document.getElementById('supervisor_position_id');

        // Prevent selecting the same position as supervisor
        positionSelect.addEventListener('change', function() {
            const selectedPositionId = this.value;
            const supervisorOptions = supervisorSelect.querySelectorAll('option');
            
            supervisorOptions.forEach(option => {
                if (option.value === selectedPositionId && selectedPositionId !== '') {
                    option.disabled = true;
                    option.style.color = '#ccc';
                } else {
                    option.disabled = false;
                    option.style.color = '';
                }
            });

            // Reset supervisor selection if it matches the selected position
            if (supervisorSelect.value === selectedPositionId) {
                supervisorSelect.value = '';
            }
        });

        // Form submission validation
        form.addEventListener('submit', function(e) {
            const positionId = positionSelect.value;
            const supervisorId = supervisorSelect.value;

            if (positionId === supervisorId && positionId !== '') {
                e.preventDefault();
                alert('Position and Supervisor Position cannot be the same.');
                return false;
            }

            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';
            submitBtn.disabled = true;

            // Re-enable button after 3 seconds (in case of validation errors)
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 3000);
        });

        console.log('Edit Workplan form initialized successfully');
    });
</script>
<?= $this->endSection() ?>
