<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .admin-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.12);
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .form-label {
        font-weight: 600;
        color: #800000;
    }

    .form-control:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    .form-select:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    .btn-success {
        background-color: #28a745;
        border-color: #28a745;
    }

    .btn-success:hover {
        background-color: #218838;
        border-color: #1e7e34;
    }

    .required {
        color: #dc3545;
    }

    .workplan-info {
        background: linear-gradient(135deg, #800000 0%, #28a745 100%);
        color: white;
        border-radius: 10px;
        padding: 1rem;
    }

    .activity-info {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        border-left: 4px solid #28a745;
    }

    .unit-group {
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .unit-group h6 {
        color: #800000;
        margin-bottom: 0.5rem;
    }

    .unit-option {
        margin-bottom: 0.5rem;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Workplan Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="workplan-info">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5 class="mb-1">
                            <i class="fas fa-clipboard-list me-2"></i><?= esc($workplan['position_name']) ?> - Fiscal Year <?= esc($workplan['fiscal_year']) ?>
                        </h5>
                        <p class="mb-0 opacity-75">
                            Supervisor: <?= esc($workplan['supervisor_name']) ?> | 
                            Current Activities: <?= $workplan['activities_count'] ?>
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities/' . $activity['id'] . '/targets') ?>" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left me-2"></i>Back to Targets
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="activity-info">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h6 class="mb-1">
                            <i class="fas fa-tasks me-2"></i>Activity: <?= esc($activity['name']) ?>
                        </h6>
                        <p class="mb-0 text-muted">
                            Type: <?= esc($activity['type']) ?> | 
                            Status: <?= esc($activity['status']) ?> | 
                            Duration: <?= date('M d', strtotime($activity['start_date'])) ?> - <?= date('M d, Y', strtotime($activity['end_date'])) ?>
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities/' . $activity['id'] . '/targets') ?>" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-arrow-left me-2"></i>Back to Targets
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-bullseye me-2"></i>Create New Target
                        </h3>
                        <p class="text-muted mb-0">
                            Define a specific target to achieve for this activity. Targets help measure progress and success.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities/' . $activity['id'] . '/targets') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Targets
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Form -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <h5 class="fw-bold mb-4" style="color: #800000;">
                    <i class="fas fa-bullseye me-2"></i>Target Information
                </h5>

                <form action="<?= site_url('workplan-management/' . $workplan['id'] . '/activities/' . $activity['id'] . '/targets') ?>" method="POST">
                    <?= csrf_field() ?>
                    
                    <div class="row g-4">
                        <!-- Target Name -->
                        <div class="col-md-8">
                            <label for="name" class="form-label">
                                Target Name <span class="required">*</span>
                            </label>
                            <input type="text" class="form-control" id="name" name="name" required
                                   placeholder="Enter target name...">
                            <div class="form-text">Provide a clear and specific name for this target.</div>
                        </div>

                        <!-- Target Type -->
                        <div class="col-md-4">
                            <label for="type" class="form-label">
                                Target Type <span class="required">*</span>
                            </label>
                            <select class="form-select" id="type" name="type" required>
                                <option value="">Select Type</option>
                                <?php foreach ($target_types as $key => $value): ?>
                                    <option value="<?= $key ?>"><?= esc($value) ?></option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Select the type of target.</div>
                        </div>

                        <!-- Target Value -->
                        <div class="col-md-4">
                            <label for="target_value" class="form-label">
                                Target Value <span class="required">*</span>
                            </label>
                            <input type="number" class="form-control" id="target_value" name="target_value" required
                                   min="0" step="0.01" placeholder="0">
                            <div class="form-text">The target value to achieve.</div>
                        </div>

                        <!-- Current Value -->
                        <div class="col-md-4">
                            <label for="current_value" class="form-label">
                                Current Value
                            </label>
                            <input type="number" class="form-control" id="current_value" name="current_value"
                                   min="0" step="0.01" placeholder="0" value="0">
                            <div class="form-text">Current progress towards the target.</div>
                        </div>

                        <!-- Measurement Unit -->
                        <div class="col-md-4">
                            <label for="unit" class="form-label">
                                Measurement Unit <span class="required">*</span>
                            </label>
                            <select class="form-select" id="unit" name="unit" required>
                                <option value="">Select Unit</option>
                                <?php foreach ($measurement_units as $category => $units): ?>
                                    <optgroup label="<?= esc($category) ?>">
                                        <?php foreach ($units as $unit): ?>
                                            <option value="<?= esc($unit) ?>"><?= esc($unit) ?></option>
                                        <?php endforeach; ?>
                                    </optgroup>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Unit of measurement for this target.</div>
                        </div>

                        <!-- Target Date -->
                        <div class="col-md-6">
                            <label for="target_date" class="form-label">
                                Target Date <span class="required">*</span>
                            </label>
                            <input type="date" class="form-control" id="target_date" name="target_date" required>
                            <div class="form-text">When should this target be achieved?</div>
                        </div>

                        <!-- Priority -->
                        <div class="col-md-6">
                            <label for="priority" class="form-label">
                                Priority <span class="required">*</span>
                            </label>
                            <select class="form-select" id="priority" name="priority" required>
                                <option value="">Select Priority</option>
                                <option value="High">High</option>
                                <option value="Medium" selected>Medium</option>
                                <option value="Low">Low</option>
                            </select>
                            <div class="form-text">Set the priority level for this target.</div>
                        </div>

                        <!-- Status -->
                        <div class="col-md-6">
                            <label for="status" class="form-label">
                                Status <span class="required">*</span>
                            </label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="Pending" selected>Pending</option>
                                <option value="In Progress">In Progress</option>
                                <option value="Completed">Completed</option>
                            </select>
                            <div class="form-text">Set the initial status of the target.</div>
                        </div>

                        <!-- Completion Percentage -->
                        <div class="col-md-6">
                            <label for="completion_percentage" class="form-label">
                                Completion Percentage
                            </label>
                            <input type="number" class="form-control" id="completion_percentage" name="completion_percentage"
                                   min="0" max="100" placeholder="0" value="0">
                            <div class="form-text">Current completion percentage (0-100%).</div>
                        </div>

                        <!-- Description -->
                        <div class="col-12">
                            <label for="description" class="form-label">
                                Description <span class="required">*</span>
                            </label>
                            <textarea class="form-control" id="description" name="description" rows="4" required
                                      placeholder="Enter detailed description of the target..."></textarea>
                            <div class="form-text">Provide a detailed description of what this target involves.</div>
                        </div>

                        <!-- Success Criteria -->
                        <div class="col-12">
                            <label for="success_criteria" class="form-label">
                                Success Criteria
                            </label>
                            <textarea class="form-control" id="success_criteria" name="success_criteria" rows="3"
                                      placeholder="Define what success looks like for this target..."></textarea>
                            <div class="form-text">Define the criteria that will determine if this target is successfully achieved.</div>
                        </div>

                        <!-- Remarks -->
                        <div class="col-12">
                            <label for="remarks" class="form-label">
                                Remarks
                            </label>
                            <textarea class="form-control" id="remarks" name="remarks" rows="2"
                                      placeholder="Enter any additional remarks or notes..."></textarea>
                            <div class="form-text">Optional remarks or additional notes.</div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-2"></i>Create Target
                                </button>
                                <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities/' . $activity['id'] . '/targets') ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Information Card -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <h6 class="fw-bold text-info mb-3">
                    <i class="fas fa-info-circle me-2"></i>Target Types Explained
                </h6>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <h6 class="text-primary">Quantitative Targets</h6>
                            <small class="text-muted">Numerical targets that can be counted or measured (e.g., "Complete 5 reports", "Train 20 staff members")</small>
                        </div>
                        <div class="mb-3">
                            <h6 class="text-warning">Performance Targets</h6>
                            <small class="text-muted">Targets based on performance metrics or KPIs (e.g., "Achieve 95% accuracy", "Reduce processing time by 50%")</small>
                        </div>
                        <div class="mb-3">
                            <h6 class="text-info">Deliverable Targets</h6>
                            <small class="text-muted">Specific outputs or products to be delivered (e.g., "Submit final report", "Launch new system")</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <h6 class="text-secondary">Timeline Targets</h6>
                            <small class="text-muted">Time-based targets focused on deadlines (e.g., "Complete by March 31", "Reduce turnaround time to 3 days")</small>
                        </div>
                        <div class="mb-3">
                            <h6 class="text-success">Quality Targets</h6>
                            <small class="text-muted">Targets focused on quality standards or criteria (e.g., "Meet ISO standards", "Achieve customer satisfaction rating of 4.5/5")</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Form validation
        const form = document.querySelector('form');
        const targetDateInput = document.getElementById('target_date');
        const targetValueInput = document.getElementById('target_value');
        const currentValueInput = document.getElementById('current_value');
        const completionPercentageInput = document.getElementById('completion_percentage');

        // Set minimum date to today
        const today = new Date().toISOString().split('T')[0];
        targetDateInput.min = today;

        // Auto-calculate completion percentage when values change
        function calculateCompletion() {
            const targetValue = parseFloat(targetValueInput.value) || 0;
            const currentValue = parseFloat(currentValueInput.value) || 0;
            
            if (targetValue > 0) {
                const percentage = Math.min(Math.round((currentValue / targetValue) * 100), 100);
                completionPercentageInput.value = percentage;
            }
        }

        targetValueInput.addEventListener('input', calculateCompletion);
        currentValueInput.addEventListener('input', calculateCompletion);

        // Validate current value doesn't exceed target value
        currentValueInput.addEventListener('input', function() {
            const targetValue = parseFloat(targetValueInput.value) || 0;
            const currentValue = parseFloat(this.value) || 0;
            
            if (targetValue > 0 && currentValue > targetValue) {
                this.setCustomValidity('Current value cannot exceed target value');
            } else {
                this.setCustomValidity('');
            }
        });

        // Form submission validation
        form.addEventListener('submit', function(e) {
            const targetDate = new Date(targetDateInput.value);
            const today = new Date();
            
            if (targetDate < today) {
                e.preventDefault();
                alert('Target date cannot be in the past.');
                return false;
            }

            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';
            submitBtn.disabled = true;

            // Re-enable button after 3 seconds (in case of validation errors)
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 3000);
        });

        console.log('Create Target form initialized successfully');
    });
</script>
<?= $this->endSection() ?>
