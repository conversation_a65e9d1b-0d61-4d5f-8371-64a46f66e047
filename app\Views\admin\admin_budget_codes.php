<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .table th {
        background-color: #f8f9fa;
        border-top: none;
        color: #800000;
        font-weight: 600;
    }

    .btn-action {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        margin: 0 0.125rem;
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .budget-amount {
        font-family: 'Courier New', monospace;
        font-weight: bold;
    }

    .amount-budgeted { color: #007bff; }
    .amount-allocated { color: #28a745; }
    .amount-spent { color: #dc3545; }
    .amount-remaining { color: #6f42c1; }

    .progress-container {
        background-color: #e9ecef;
        border-radius: 4px;
        height: 8px;
        overflow: hidden;
        position: relative;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #28a745, #20c997);
        transition: width 0.3s ease;
    }

    .budget-summary-card {
        background: linear-gradient(135deg, #800000 0%, #a00000 100%);
        border-radius: 10px;
        color: white;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .summary-item {
        text-align: center;
        padding: 0.5rem;
    }

    .summary-amount {
        font-size: 1.5rem;
        font-weight: bold;
        color: #28a745;
    }

    .category-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }

    .utilization-bar {
        background-color: #e9ecef;
        border-radius: 10px;
        height: 20px;
        overflow: hidden;
        position: relative;
    }

    .utilization-fill {
        height: 100%;
        background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
        transition: width 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.75rem;
        font-weight: bold;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-code me-2"></i>Budget Codes Management
                        </h3>
                        <p class="text-muted mb-1">
                            Budget Book: <strong class="text-primary"><?= esc($budget_book['name']) ?></strong>
                        </p>
                        <p class="text-muted mb-0 small">
                            Manage budget codes and their allocated amounts within this budget book.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="btn-group">
                            <a href="<?= site_url('admin/budget-books') ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Budget Books
                            </a>
                            <a href="<?= site_url('admin/budget-books/' . $budget_book['id'] . '/codes/create') ?>" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>Create Budget Code
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Budget Summary -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="budget-summary-card">
                <div class="row">
                    <div class="col-md-2">
                        <div class="summary-item">
                            <div class="summary-amount">K <?= number_format($budget_summary['total_budgeted'] / 1000000, 0) ?>M</div>
                            <div class="small">Total Budgeted</div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="summary-item">
                            <div class="summary-amount">K <?= number_format($budget_summary['total_allocated'] / 1000000, 0) ?>M</div>
                            <div class="small">Total Allocated</div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="summary-item">
                            <div class="summary-amount">K <?= number_format($budget_summary['total_spent'] / 1000000, 0) ?>M</div>
                            <div class="small">Total Spent</div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="summary-item">
                            <div class="summary-amount">K <?= number_format($budget_summary['remaining_budget'] / 1000000, 0) ?>M</div>
                            <div class="small">Remaining</div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="summary-item">
                            <div class="summary-amount"><?= $budget_summary['utilization_rate'] ?>%</div>
                            <div class="small">Utilization</div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="summary-item">
                            <div class="summary-amount"><?= $budget_summary['total_codes'] ?></div>
                            <div class="small">Budget Codes</div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="utilization-bar">
                            <div class="utilization-fill" style="width: <?= min($budget_summary['utilization_rate'], 100) ?>%;">
                                <?= $budget_summary['utilization_rate'] ?>% Utilized
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Budget Codes List -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="fw-bold mb-0" style="color: #800000;">
                        <i class="fas fa-list-ol me-2"></i>Budget Codes & Amounts
                    </h5>
                    <span class="badge bg-info">
                        <?= count($budget_codes) ?> Code<?= count($budget_codes) !== 1 ? 's' : '' ?>
                    </span>
                </div>

                <?php if (empty($budget_codes)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-code fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Budget Codes Found</h5>
                        <p class="text-muted">Create your first budget code within this budget book.</p>
                        <a href="<?= site_url('admin/budget-books/' . $budget_book['id'] . '/codes/create') ?>" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>Create First Budget Code
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Code & Name</th>
                                    <th>Category</th>
                                    <th>Budgeted Amount</th>
                                    <th>Allocated Amount</th>
                                    <th>Spent Amount</th>
                                    <th>Remaining</th>
                                    <th>Utilization</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($budget_codes as $code): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong class="text-primary"><?= esc($code['code']) ?></strong>
                                            <br>
                                            <strong><?= esc($code['name']) ?></strong>
                                            <br>
                                            <small class="text-muted"><?= esc($code['description']) ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary category-badge">
                                            <?= esc($code['category']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="budget-amount amount-budgeted">
                                            K <?= number_format($code['budgeted_amount'] / 1000000, 1) ?>M
                                        </span>
                                    </td>
                                    <td>
                                        <span class="budget-amount amount-allocated">
                                            K <?= number_format($code['allocated_amount'] / 1000000, 1) ?>M
                                        </span>
                                    </td>
                                    <td>
                                        <span class="budget-amount amount-spent">
                                            K <?= number_format($code['spent_amount'] / 1000000, 1) ?>M
                                        </span>
                                    </td>
                                    <td>
                                        <span class="budget-amount amount-remaining">
                                            K <?= number_format(($code['allocated_amount'] - $code['spent_amount']) / 1000000, 1) ?>M
                                        </span>
                                    </td>
                                    <td>
                                        <?php 
                                        $utilization = $code['allocated_amount'] > 0 ? 
                                            round(($code['spent_amount'] / $code['allocated_amount']) * 100, 1) : 0;
                                        ?>
                                        <div class="progress-container" style="width: 80px;">
                                            <div class="progress-fill" style="width: <?= min($utilization, 100) ?>%;"></div>
                                        </div>
                                        <small class="text-muted"><?= $utilization ?>%</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= site_url('admin/budget-books/' . $budget_book['id'] . '/codes/' . $code['id'] . '/edit') ?>" 
                                               class="btn btn-outline-primary btn-action" title="Edit Budget Code">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form method="POST" action="<?= site_url('admin/budget-books/' . $budget_book['id'] . '/codes/' . $code['id']) ?>" class="d-inline">
                                                <?= csrf_field() ?>
                                                <input type="hidden" name="_method" value="DELETE">
                                                <button type="submit" class="btn btn-outline-danger btn-action" 
                                                        title="Delete Budget Code"
                                                        onclick="return confirm('Are you sure you want to delete this budget code? This action cannot be undone.')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Navigation Help -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="admin-card p-3">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h6 class="mb-1 text-primary">
                            <i class="fas fa-info-circle me-2"></i>Navigation Flow
                        </h6>
                        <p class="mb-0 text-muted small">
                            Budget Books → <strong>Budget Codes with Amounts</strong>. You've reached the final level where budget codes and their allocated amounts are managed.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="btn-group">
                            <a href="<?= site_url('admin/budget-books') ?>" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-arrow-left me-2"></i>Back to Budget Books
                            </a>
                            <a href="<?= site_url('admin/budget-books/' . $budget_book['id'] . '/codes/create') ?>" class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-2"></i>Add Budget Code
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-hide alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            setTimeout(function() {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, 5000);
        });

        console.log('Budget Codes Management initialized successfully');
    });
</script>
<?= $this->endSection() ?>
