<?php

namespace App\Controllers;

class WorkplanController extends BaseController
{
    /**
     * Display workplan list (GET request)
     *
     * @return string
     */
    public function index()
    {
        $data = [
            'page_title' => 'Workplan Management',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Workplan Management', 'url' => '']
            ],
            'workplans' => $this->getDummyWorkplans(),
            'stats' => $this->getWorkplanStats()
        ];

        return view('workplans/workplans_index', $data);
    }

    /**
     * Show create workplan form (GET request)
     *
     * @return string
     */
    public function create()
    {
        $data = [
            'page_title' => 'Create New Workplan',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Workplan Management', 'url' => site_url('workplan-management')],
                ['title' => 'Create Workplan', 'url' => '']
            ],
            'positions' => $this->getDummyPositions(),
            'fiscal_years' => $this->getFiscalYears()
        ];

        return view('workplans/workplans_create', $data);
    }

    /**
     * Store new workplan (POST request)
     *
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function store()
    {
        // In real implementation, validate and save to database
        // For demo, just redirect with success message

        session()->setFlashdata('success', 'Workplan created successfully!');
        return redirect()->to(site_url('workplan-management'));
    }

    /**
     * Show edit workplan form (GET request)
     *
     * @param int $id
     * @return string
     */
    public function edit($id)
    {
        $workplan = $this->getDummyWorkplan($id);

        if (!$workplan) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Workplan not found');
        }

        $data = [
            'page_title' => 'Edit Workplan',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Workplan Management', 'url' => site_url('workplan-management')],
                ['title' => 'Edit Workplan', 'url' => '']
            ],
            'workplan' => $workplan,
            'positions' => $this->getDummyPositions(),
            'fiscal_years' => $this->getFiscalYears()
        ];

        return view('workplans/workplans_edit', $data);
    }

    /**
     * Update workplan (PUT request)
     *
     * @param int $id
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function update($id)
    {
        // In real implementation, validate and update database
        // For demo, just redirect with success message

        session()->setFlashdata('success', 'Workplan updated successfully!');
        return redirect()->to(site_url('workplan-management'));
    }

    /**
     * Delete workplan (DELETE request)
     *
     * @param int $id
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function delete($id)
    {
        // In real implementation, delete from database
        // For demo, just redirect with success message

        session()->setFlashdata('success', 'Workplan deleted successfully!');
        return redirect()->to(site_url('workplan-management'));
    }

    /**
     * Display activities for a workplan (GET request)
     *
     * @param int $workplanId
     * @return string
     */
    public function activities($workplanId)
    {
        $workplan = $this->getDummyWorkplan($workplanId);

        if (!$workplan) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Workplan not found');
        }

        $data = [
            'page_title' => 'Workplan Activities',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Workplan Management', 'url' => site_url('workplan-management')],
                ['title' => 'Activities', 'url' => '']
            ],
            'workplan' => $workplan,
            'activities' => $this->getDummyActivities($workplanId),
            'stats' => $this->getActivityStats($workplanId)
        ];

        return view('workplans/workplans_activities', $data);
    }

    /**
     * Show create activity form (GET request)
     *
     * @param int $workplanId
     * @return string
     */
    public function createActivity($workplanId)
    {
        $workplan = $this->getDummyWorkplan($workplanId);

        if (!$workplan) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Workplan not found');
        }

        $data = [
            'page_title' => 'Create New Activity',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Workplan Management', 'url' => site_url('workplan-management')],
                ['title' => 'Activities', 'url' => site_url('workplan-management/' . $workplanId . '/activities')],
                ['title' => 'Create Activity', 'url' => '']
            ],
            'workplan' => $workplan,
            'positions' => $this->getDummyPositions(),
            'activity_types' => $this->getActivityTypes()
        ];

        return view('workplans/workplans_activities_create', $data);
    }

    /**
     * Store new activity (POST request)
     *
     * @param int $workplanId
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function storeActivity($workplanId)
    {
        // In real implementation, validate and save to database
        // For demo, just redirect with success message

        session()->setFlashdata('success', 'Activity created successfully!');
        return redirect()->to(site_url('workplan-management/' . $workplanId . '/activities'));
    }

    /**
     * Show edit activity form (GET request)
     *
     * @param int $workplanId
     * @param int $activityId
     * @return string
     */
    public function editActivity($workplanId, $activityId)
    {
        $workplan = $this->getDummyWorkplan($workplanId);
        $activity = $this->getDummyActivity($activityId);

        if (!$workplan || !$activity) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Workplan or Activity not found');
        }

        $data = [
            'page_title' => 'Edit Activity',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Workplan Management', 'url' => site_url('workplan-management')],
                ['title' => 'Activities', 'url' => site_url('workplan-management/' . $workplanId . '/activities')],
                ['title' => 'Edit Activity', 'url' => '']
            ],
            'workplan' => $workplan,
            'activity' => $activity,
            'positions' => $this->getDummyPositions(),
            'activity_types' => $this->getActivityTypes()
        ];

        return view('workplans/workplans_activities_edit', $data);
    }

    /**
     * Update activity (PUT request)
     *
     * @param int $workplanId
     * @param int $activityId
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function updateActivity($workplanId, $activityId)
    {
        // In real implementation, validate and update database
        // For demo, just redirect with success message

        session()->setFlashdata('success', 'Activity updated successfully!');
        return redirect()->to(site_url('workplan-management/' . $workplanId . '/activities'));
    }

    /**
     * Delete activity (DELETE request)
     *
     * @param int $workplanId
     * @param int $activityId
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function deleteActivity($workplanId, $activityId)
    {
        // In real implementation, delete from database
        // For demo, just redirect with success message

        session()->setFlashdata('success', 'Activity deleted successfully!');
        return redirect()->to(site_url('workplan-management/' . $workplanId . '/activities'));
    }

    /**
     * Show activity linking form (GET request)
     *
     * @param int $workplanId
     * @param int $activityId
     * @return string
     */
    public function linkActivity($workplanId, $activityId)
    {
        $workplan = $this->getDummyWorkplan($workplanId);
        $activity = $this->getDummyActivity($activityId);

        if (!$workplan || !$activity) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Workplan or Activity not found');
        }

        $data = [
            'page_title' => 'Link Activity Resources',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Workplan Management', 'url' => site_url('workplan-management')],
                ['title' => 'Activities', 'url' => site_url('workplan-management/' . $workplanId . '/activities')],
                ['title' => 'Link Resources', 'url' => '']
            ],
            'workplan' => $workplan,
            'activity' => $activity,
            'plans' => $this->getDummyPlans(),
            'budget_codes' => $this->getDummyBudgetCodes(),
            'assets' => $this->getDummyAssets(),
            'officers' => $this->getDummyOfficers(),
            'current_links' => $this->getDummyActivityLinks($activityId)
        ];

        return view('workplans/workplans_activities_link', $data);
    }

    /**
     * Store activity links (POST request)
     *
     * @param int $workplanId
     * @param int $activityId
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function storeLinkActivity($workplanId, $activityId)
    {
        // In real implementation, validate and save links to database
        // For demo, just redirect with success message

        session()->setFlashdata('success', 'Activity resources linked successfully!');
        return redirect()->to(site_url('workplan-management/' . $workplanId . '/activities'));
    }

    /**
     * Show activity targets list (GET request)
     *
     * @param int $workplanId
     * @param int $activityId
     * @return string
     */
    public function targets($workplanId, $activityId)
    {
        $workplan = $this->getDummyWorkplan($workplanId);
        $activity = $this->getDummyActivity($activityId);

        if (!$workplan || !$activity) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Workplan or Activity not found');
        }

        $data = [
            'page_title' => 'Activity Targets',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Workplan Management', 'url' => site_url('workplan-management')],
                ['title' => 'Activities', 'url' => site_url('workplan-management/' . $workplanId . '/activities')],
                ['title' => 'Targets', 'url' => '']
            ],
            'workplan' => $workplan,
            'activity' => $activity,
            'targets' => $this->getDummyActivityTargets($activityId),
            'stats' => $this->getTargetStats($activityId)
        ];

        return view('workplans/workplans_activities_targets', $data);
    }

    /**
     * Show create target form (GET request)
     *
     * @param int $workplanId
     * @param int $activityId
     * @return string
     */
    public function createTarget($workplanId, $activityId)
    {
        $workplan = $this->getDummyWorkplan($workplanId);
        $activity = $this->getDummyActivity($activityId);

        if (!$workplan || !$activity) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Workplan or Activity not found');
        }

        $data = [
            'page_title' => 'Create Target',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Workplan Management', 'url' => site_url('workplan-management')],
                ['title' => 'Activities', 'url' => site_url('workplan-management/' . $workplanId . '/activities')],
                ['title' => 'Targets', 'url' => site_url('workplan-management/' . $workplanId . '/activities/' . $activityId . '/targets')],
                ['title' => 'Create Target', 'url' => '']
            ],
            'workplan' => $workplan,
            'activity' => $activity,
            'target_types' => $this->getTargetTypes(),
            'measurement_units' => $this->getMeasurementUnits()
        ];

        return view('workplans/workplans_activities_targets_create', $data);
    }

    /**
     * Store new target (POST request)
     *
     * @param int $workplanId
     * @param int $activityId
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function storeTarget($workplanId, $activityId)
    {
        // In real implementation, validate and save to database
        // For demo, just redirect with success message

        session()->setFlashdata('success', 'Target created successfully!');
        return redirect()->to(site_url('workplan-management/' . $workplanId . '/activities/' . $activityId . '/targets'));
    }

    /**
     * Show edit target form (GET request)
     *
     * @param int $workplanId
     * @param int $activityId
     * @param int $targetId
     * @return string
     */
    public function editTarget($workplanId, $activityId, $targetId)
    {
        $workplan = $this->getDummyWorkplan($workplanId);
        $activity = $this->getDummyActivity($activityId);
        $target = $this->getDummyTarget($targetId);

        if (!$workplan || !$activity || !$target) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Workplan, Activity or Target not found');
        }

        $data = [
            'page_title' => 'Edit Target',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Workplan Management', 'url' => site_url('workplan-management')],
                ['title' => 'Activities', 'url' => site_url('workplan-management/' . $workplanId . '/activities')],
                ['title' => 'Targets', 'url' => site_url('workplan-management/' . $workplanId . '/activities/' . $activityId . '/targets')],
                ['title' => 'Edit Target', 'url' => '']
            ],
            'workplan' => $workplan,
            'activity' => $activity,
            'target' => $target,
            'target_types' => $this->getTargetTypes(),
            'measurement_units' => $this->getMeasurementUnits()
        ];

        return view('workplans/workplans_activities_targets_edit', $data);
    }

    /**
     * Update target (PUT request)
     *
     * @param int $workplanId
     * @param int $activityId
     * @param int $targetId
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function updateTarget($workplanId, $activityId, $targetId)
    {
        // In real implementation, validate and update database
        // For demo, just redirect with success message

        session()->setFlashdata('success', 'Target updated successfully!');
        return redirect()->to(site_url('workplan-management/' . $workplanId . '/activities/' . $activityId . '/targets'));
    }

    /**
     * Delete target (DELETE request)
     *
     * @param int $workplanId
     * @param int $activityId
     * @param int $targetId
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function deleteTarget($workplanId, $activityId, $targetId)
    {
        // In real implementation, delete from database
        // For demo, just redirect with success message

        session()->setFlashdata('success', 'Target deleted successfully!');
        return redirect()->to(site_url('workplan-management/' . $workplanId . '/activities/' . $activityId . '/targets'));
    }

    // ========================================
    // DUMMY DATA METHODS
    // ========================================

    /**
     * Get dummy workplans data
     *
     * @return array
     */
    private function getDummyWorkplans()
    {
        return [
            [
                'id' => 1,
                'position_name' => 'Chief Executive Officer',
                'supervisor_name' => 'Board Chairman',
                'fiscal_year' => 2024,
                'activities_count' => 12,
                'completed_activities' => 8,
                'status' => 'Active',
                'created_at' => '2024-01-15 09:00:00',
                'updated_at' => '2024-01-20 14:30:00'
            ],
            [
                'id' => 2,
                'position_name' => 'Finance Manager',
                'supervisor_name' => 'Chief Executive Officer',
                'fiscal_year' => 2024,
                'activities_count' => 15,
                'completed_activities' => 10,
                'status' => 'Active',
                'created_at' => '2024-01-16 10:15:00',
                'updated_at' => '2024-01-22 11:45:00'
            ],
            [
                'id' => 3,
                'position_name' => 'Human Resources Manager',
                'supervisor_name' => 'Chief Executive Officer',
                'fiscal_year' => 2024,
                'activities_count' => 18,
                'completed_activities' => 12,
                'status' => 'Active',
                'created_at' => '2024-01-17 08:30:00',
                'updated_at' => '2024-01-23 16:20:00'
            ],
            [
                'id' => 4,
                'position_name' => 'Operations Manager',
                'supervisor_name' => 'Chief Executive Officer',
                'fiscal_year' => 2024,
                'activities_count' => 20,
                'completed_activities' => 14,
                'status' => 'Active',
                'created_at' => '2024-01-18 11:00:00',
                'updated_at' => '2024-01-24 13:15:00'
            ],
            [
                'id' => 5,
                'position_name' => 'IT Manager',
                'supervisor_name' => 'Operations Manager',
                'fiscal_year' => 2023,
                'activities_count' => 25,
                'completed_activities' => 25,
                'status' => 'Completed',
                'created_at' => '2023-01-15 09:00:00',
                'updated_at' => '2023-12-30 17:00:00'
            ]
        ];
    }

    /**
     * Get single dummy workplan
     *
     * @param int $id
     * @return array|null
     */
    private function getDummyWorkplan($id)
    {
        $workplans = $this->getDummyWorkplans();
        foreach ($workplans as $workplan) {
            if ($workplan['id'] == $id) {
                return $workplan;
            }
        }
        return null;
    }

    /**
     * Get workplan statistics
     *
     * @return array
     */
    private function getWorkplanStats()
    {
        return [
            'total_workplans' => 5,
            'active_workplans' => 4,
            'completed_workplans' => 1,
            'total_activities' => 90,
            'completed_activities' => 69,
            'completion_rate' => 77
        ];
    }

    /**
     * Get dummy positions
     *
     * @return array
     */
    private function getDummyPositions()
    {
        return [
            ['id' => 1, 'name' => 'Chief Executive Officer', 'group' => 'Executive'],
            ['id' => 2, 'name' => 'Finance Manager', 'group' => 'Finance'],
            ['id' => 3, 'name' => 'Human Resources Manager', 'group' => 'Human Resources'],
            ['id' => 4, 'name' => 'Operations Manager', 'group' => 'Operations'],
            ['id' => 5, 'name' => 'IT Manager', 'group' => 'Information Technology'],
            ['id' => 6, 'name' => 'Marketing Manager', 'group' => 'Marketing'],
            ['id' => 7, 'name' => 'Project Manager', 'group' => 'Projects'],
            ['id' => 8, 'name' => 'Senior Accountant', 'group' => 'Finance'],
            ['id' => 9, 'name' => 'HR Officer', 'group' => 'Human Resources'],
            ['id' => 10, 'name' => 'IT Support Officer', 'group' => 'Information Technology']
        ];
    }

    /**
     * Get fiscal years
     *
     * @return array
     */
    private function getFiscalYears()
    {
        return [2022, 2023, 2024, 2025, 2026];
    }

    /**
     * Get dummy activities for a workplan
     *
     * @param int $workplanId
     * @return array
     */
    private function getDummyActivities($workplanId)
    {
        return [
            [
                'id' => 1,
                'name' => 'Quarterly Budget Review',
                'position_name' => 'Finance Manager',
                'type' => 'Recurrent',
                'status' => 'Completed',
                'start_date' => '2024-01-01',
                'end_date' => '2024-03-31',
                'budget_code' => 'REV-001',
                'linked_plan' => 'Corporate Plan 2024',
                'assets_count' => 2,
                'officers_count' => 3,
                'created_at' => '2024-01-15 09:00:00'
            ],
            [
                'id' => 2,
                'name' => 'Staff Performance Evaluation',
                'position_name' => 'HR Manager',
                'type' => 'Legislated',
                'status' => 'In Progress',
                'start_date' => '2024-02-01',
                'end_date' => '2024-02-28',
                'budget_code' => 'EXP-002',
                'linked_plan' => 'Development Plan 2024',
                'assets_count' => 1,
                'officers_count' => 5,
                'created_at' => '2024-01-16 10:15:00'
            ],
            [
                'id' => 3,
                'name' => 'IT Infrastructure Upgrade',
                'position_name' => 'IT Manager',
                'type' => 'Project',
                'status' => 'Pending',
                'start_date' => '2024-03-01',
                'end_date' => '2024-06-30',
                'budget_code' => 'CAP-003',
                'linked_plan' => 'Corporate Plan 2024',
                'assets_count' => 15,
                'officers_count' => 8,
                'created_at' => '2024-01-17 08:30:00'
            ],
            [
                'id' => 4,
                'name' => 'Customer Service Training',
                'position_name' => 'Operations Manager',
                'type' => 'Recurrent',
                'status' => 'Completed',
                'start_date' => '2024-01-15',
                'end_date' => '2024-01-30',
                'budget_code' => 'EXP-004',
                'linked_plan' => 'Corporate Plan 2024',
                'assets_count' => 3,
                'officers_count' => 12,
                'created_at' => '2024-01-18 11:00:00'
            ]
        ];
    }

    /**
     * Get single dummy activity
     *
     * @param int $id
     * @return array|null
     */
    private function getDummyActivity($id)
    {
        $activities = $this->getDummyActivities(1); // Use workplan 1 as default
        foreach ($activities as $activity) {
            if ($activity['id'] == $id) {
                return $activity;
            }
        }
        return null;
    }

    /**
     * Get activity statistics for a workplan
     *
     * @param int $workplanId
     * @return array
     */
    private function getActivityStats($workplanId)
    {
        return [
            'total_activities' => 4,
            'completed_activities' => 2,
            'in_progress_activities' => 1,
            'pending_activities' => 1,
            'completion_rate' => 50
        ];
    }

    /**
     * Get activity types
     *
     * @return array
     */
    private function getActivityTypes()
    {
        return [
            'Recurrent' => 'Recurrent Activity',
            'Project' => 'Project Activity',
            'Legislated' => 'Legislated Activity'
        ];
    }

    /**
     * Get dummy plans
     *
     * @return array
     */
    private function getDummyPlans()
    {
        return [
            ['id' => 1, 'name' => 'Corporate Plan 2024', 'type' => 'Corporate'],
            ['id' => 2, 'name' => 'Development Plan 2024', 'type' => 'Development'],
            ['id' => 3, 'name' => 'Strategic Plan 2024-2026', 'type' => 'Corporate'],
            ['id' => 4, 'name' => 'Infrastructure Development Plan', 'type' => 'Development']
        ];
    }

    /**
     * Get dummy budget codes
     *
     * @return array
     */
    private function getDummyBudgetCodes()
    {
        return [
            ['id' => 1, 'code' => 'REV-001', 'name' => 'Service Revenue', 'type' => 'Revenue'],
            ['id' => 2, 'code' => 'EXP-002', 'name' => 'Staff Training', 'type' => 'Expenditure'],
            ['id' => 3, 'code' => 'CAP-003', 'name' => 'IT Equipment', 'type' => 'Capital'],
            ['id' => 4, 'code' => 'EXP-004', 'name' => 'Professional Development', 'type' => 'Expenditure'],
            ['id' => 5, 'code' => 'REV-005', 'name' => 'Consultation Fees', 'type' => 'Revenue']
        ];
    }

    /**
     * Get dummy assets
     *
     * @return array
     */
    private function getDummyAssets()
    {
        return [
            ['id' => 1, 'name' => 'Conference Room A', 'type' => 'Facility'],
            ['id' => 2, 'name' => 'Laptop - Dell Latitude', 'type' => 'Equipment'],
            ['id' => 3, 'name' => 'Projector - Epson', 'type' => 'Equipment'],
            ['id' => 4, 'name' => 'Vehicle - Toyota Hilux', 'type' => 'Vehicle'],
            ['id' => 5, 'name' => 'Training Materials', 'type' => 'Resource']
        ];
    }

    /**
     * Get dummy officers
     *
     * @return array
     */
    private function getDummyOfficers()
    {
        return [
            ['id' => 1, 'name' => 'John Smith', 'position' => 'Senior Accountant'],
            ['id' => 2, 'name' => 'Mary Johnson', 'position' => 'HR Officer'],
            ['id' => 3, 'name' => 'David Wilson', 'position' => 'IT Support Officer'],
            ['id' => 4, 'name' => 'Sarah Brown', 'position' => 'Project Coordinator'],
            ['id' => 5, 'name' => 'Michael Davis', 'position' => 'Operations Assistant']
        ];
    }

    /**
     * Get dummy activity links
     *
     * @param int $activityId
     * @return array
     */
    private function getDummyActivityLinks($activityId)
    {
        return [
            'plans' => [1, 2],
            'budget_codes' => [1, 3],
            'assets' => [1, 2, 3],
            'officers' => [1, 2, 3, 4]
        ];
    }

    /**
     * Get dummy activity targets
     *
     * @param int $activityId
     * @return array
     */
    private function getDummyActivityTargets($activityId)
    {
        return [
            [
                'id' => 1,
                'activity_id' => $activityId,
                'name' => 'Complete Budget Analysis Report',
                'description' => 'Prepare comprehensive budget analysis report covering all departments',
                'type' => 'Deliverable',
                'target_value' => 1,
                'current_value' => 0,
                'unit' => 'Report',
                'target_date' => '2024-03-15',
                'priority' => 'High',
                'status' => 'Pending',
                'completion_percentage' => 0,
                'created_at' => '2024-01-15 09:00:00',
                'updated_at' => '2024-01-15 09:00:00'
            ],
            [
                'id' => 2,
                'activity_id' => $activityId,
                'name' => 'Conduct Department Meetings',
                'description' => 'Hold meetings with all department heads to discuss budget allocations',
                'type' => 'Quantitative',
                'target_value' => 5,
                'current_value' => 3,
                'unit' => 'Meetings',
                'target_date' => '2024-02-28',
                'priority' => 'Medium',
                'status' => 'In Progress',
                'completion_percentage' => 60,
                'created_at' => '2024-01-16 10:15:00',
                'updated_at' => '2024-01-20 14:30:00'
            ],
            [
                'id' => 3,
                'activity_id' => $activityId,
                'name' => 'Achieve Budget Accuracy',
                'description' => 'Maintain budget variance within acceptable limits',
                'type' => 'Performance',
                'target_value' => 95,
                'current_value' => 88,
                'unit' => 'Percentage',
                'target_date' => '2024-03-31',
                'priority' => 'High',
                'status' => 'In Progress',
                'completion_percentage' => 75,
                'created_at' => '2024-01-17 08:30:00',
                'updated_at' => '2024-01-22 11:45:00'
            ],
            [
                'id' => 4,
                'activity_id' => $activityId,
                'name' => 'Train Staff on New Procedures',
                'description' => 'Provide training to finance staff on updated budget procedures',
                'type' => 'Quantitative',
                'target_value' => 12,
                'current_value' => 12,
                'unit' => 'Staff Members',
                'target_date' => '2024-02-15',
                'priority' => 'Medium',
                'status' => 'Completed',
                'completion_percentage' => 100,
                'created_at' => '2024-01-18 11:00:00',
                'updated_at' => '2024-02-15 16:30:00'
            ],
            [
                'id' => 5,
                'activity_id' => $activityId,
                'name' => 'Reduce Processing Time',
                'description' => 'Improve efficiency by reducing budget processing time',
                'type' => 'Performance',
                'target_value' => 3,
                'current_value' => 4.5,
                'unit' => 'Days',
                'target_date' => '2024-04-30',
                'priority' => 'Low',
                'status' => 'Pending',
                'completion_percentage' => 25,
                'created_at' => '2024-01-19 13:15:00',
                'updated_at' => '2024-01-25 09:20:00'
            ]
        ];
    }

    /**
     * Get single dummy target
     *
     * @param int $id
     * @return array|null
     */
    private function getDummyTarget($id)
    {
        $targets = $this->getDummyActivityTargets(1); // Use activity 1 as default
        foreach ($targets as $target) {
            if ($target['id'] == $id) {
                return $target;
            }
        }
        return null;
    }

    /**
     * Get target statistics
     *
     * @param int $activityId
     * @return array
     */
    private function getTargetStats($activityId)
    {
        return [
            'total_targets' => 5,
            'completed_targets' => 1,
            'in_progress_targets' => 2,
            'pending_targets' => 2,
            'completion_rate' => 20,
            'average_progress' => 52
        ];
    }

    /**
     * Get target types
     *
     * @return array
     */
    private function getTargetTypes()
    {
        return [
            'Quantitative' => 'Quantitative Target (Numbers/Counts)',
            'Performance' => 'Performance Target (Metrics/KPIs)',
            'Deliverable' => 'Deliverable Target (Outputs/Products)',
            'Timeline' => 'Timeline Target (Time-based)',
            'Quality' => 'Quality Target (Standards/Criteria)'
        ];
    }

    /**
     * Get measurement units
     *
     * @return array
     */
    private function getMeasurementUnits()
    {
        return [
            'Count' => ['Units', 'Items', 'Pieces', 'Numbers'],
            'People' => ['Staff Members', 'Participants', 'Attendees', 'Users'],
            'Documents' => ['Reports', 'Forms', 'Applications', 'Certificates'],
            'Events' => ['Meetings', 'Workshops', 'Training Sessions', 'Conferences'],
            'Time' => ['Hours', 'Days', 'Weeks', 'Months'],
            'Performance' => ['Percentage', 'Score', 'Rating', 'Index'],
            'Financial' => ['Kina (K)', 'Budget', 'Cost', 'Revenue']
        ];
    }
}
