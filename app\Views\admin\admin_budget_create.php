<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .form-label {
        color: #800000;
        font-weight: 600;
    }

    .form-control:focus, .form-select:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    .btn-success {
        background-color: #28a745;
        border-color: #28a745;
    }

    .btn-success:hover {
        background-color: #218838;
        border-color: #1e7e34;
    }

    .budget-info {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .currency-input {
        position: relative;
    }

    .currency-symbol {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #28a745;
        font-weight: bold;
        z-index: 10;
    }

    .currency-input .form-control {
        padding-left: 35px;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-plus-circle me-2"></i>Create New Budget Book
                        </h3>
                        <p class="text-muted mb-0">
                            Create a new budget book to organize and manage budget codes and allocations.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= site_url('admin/budget-books') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Budget Books
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Form -->
    <div class="row">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="admin-card p-4">
                <form method="POST" action="<?= site_url('admin/budget-books') ?>" id="createBudgetBookForm">
                    <?= csrf_field() ?>
                    
                    <div class="mb-4">
                        <label for="name" class="form-label">
                            <i class="fas fa-book me-2"></i>Budget Book Name *
                        </label>
                        <input type="text" 
                               class="form-control form-control-lg" 
                               id="name" 
                               name="name" 
                               placeholder="Enter budget book name (e.g., PNG National Budget 2024)"
                               required
                               maxlength="255">
                        <div class="form-text">
                            Enter a descriptive name for your budget book.
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-4">
                                <label for="fiscal_year" class="form-label">
                                    <i class="fas fa-calendar me-2"></i>Fiscal Year *
                                </label>
                                <select class="form-select form-select-lg" id="fiscal_year" name="fiscal_year" required>
                                    <option value="">Select Fiscal Year</option>
                                    <?php for ($year = 2024; $year <= 2030; $year++): ?>
                                        <option value="<?= $year ?>" <?= $year == 2024 ? 'selected' : '' ?>><?= $year ?></option>
                                    <?php endfor; ?>
                                </select>
                                <div class="form-text">
                                    Select the fiscal year for this budget book.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-4">
                                <label for="total_budget" class="form-label">
                                    <i class="fas fa-money-bill-wave me-2"></i>Total Budget Amount (PGK) *
                                </label>
                                <div class="currency-input">
                                    <span class="currency-symbol">K</span>
                                    <input type="number" 
                                           class="form-control form-control-lg" 
                                           id="total_budget" 
                                           name="total_budget" 
                                           placeholder="0.00"
                                           step="0.01"
                                           min="0"
                                           required>
                                </div>
                                <div class="form-text">
                                    Enter the total budget amount in Papua New Guinea Kina.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="description" class="form-label">
                            <i class="fas fa-align-left me-2"></i>Description
                        </label>
                        <textarea class="form-control" 
                                  id="description" 
                                  name="description" 
                                  rows="4"
                                  placeholder="Enter a detailed description of the budget book's purpose and scope"
                                  maxlength="1000"></textarea>
                        <div class="form-text">
                            Provide a comprehensive description of the budget book's purpose and coverage.
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="status" class="form-label">
                            <i class="fas fa-toggle-on me-2"></i>Status
                        </label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="Draft" selected>Draft</option>
                            <option value="Under Review">Under Review</option>
                            <option value="Active">Active</option>
                        </select>
                        <div class="form-text">
                            Set the initial status of the budget book.
                        </div>
                    </div>

                    <!-- Budget Information -->
                    <div class="budget-info">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-info-circle me-2"></i>Budget Book Information
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p class="text-muted mb-2">
                                    <strong>Next Steps:</strong>
                                </p>
                                <ul class="text-muted mb-0">
                                    <li>Create budget codes within the budget book</li>
                                    <li>Assign budget amounts to each code</li>
                                    <li>Track allocations and expenditures</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <p class="text-muted mb-2">
                                    <strong>Budget Code Structure:</strong>
                                </p>
                                <ul class="text-muted mb-0">
                                    <li>Format: XX.XX.XXX (e.g., 01.01.001)</li>
                                    <li>Categories: Personnel, Goods & Services, Capital</li>
                                    <li>Each code has budgeted and allocated amounts</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex gap-3">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-save me-2"></i>Create Budget Book
                        </button>
                        <a href="<?= site_url('admin/budget-books') ?>" class="btn btn-outline-secondary btn-lg">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Help Section -->
    <div class="row mt-4">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="admin-card p-3">
                <h6 class="mb-2 text-primary">
                    <i class="fas fa-question-circle me-2"></i>Budget Book Guidelines
                </h6>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-unstyled small text-muted">
                            <li><i class="fas fa-check text-success me-2"></i>Use clear, descriptive names</li>
                            <li><i class="fas fa-check text-success me-2"></i>Include fiscal year in the name</li>
                            <li><i class="fas fa-check text-success me-2"></i>Set realistic budget amounts</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-unstyled small text-muted">
                            <li><i class="fas fa-lightbulb text-warning me-2"></i>Examples: "PNG National Budget 2024"</li>
                            <li><i class="fas fa-lightbulb text-warning me-2"></i>Start with Draft status for review</li>
                            <li><i class="fas fa-lightbulb text-warning me-2"></i>Add detailed descriptions for clarity</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('createBudgetBookForm');
        const nameInput = document.getElementById('name');
        const budgetInput = document.getElementById('total_budget');
        
        // Form validation
        form.addEventListener('submit', function(e) {
            const name = nameInput.value.trim();
            const budget = parseFloat(budgetInput.value);
            
            if (name.length < 3) {
                e.preventDefault();
                alert('Budget book name must be at least 3 characters long.');
                nameInput.focus();
                return false;
            }
            
            if (isNaN(budget) || budget <= 0) {
                e.preventDefault();
                alert('Please enter a valid budget amount greater than 0.');
                budgetInput.focus();
                return false;
            }
            
            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';
            submitBtn.disabled = true;
        });
        
        // Format budget input
        budgetInput.addEventListener('input', function() {
            const value = parseFloat(this.value);
            if (!isNaN(value) && value > 0) {
                const formatted = (value / 1000000).toFixed(1);
                const helpText = this.parentNode.parentNode.querySelector('.form-text');
                helpText.innerHTML = `Enter the total budget amount in Papua New Guinea Kina. (≈ K ${formatted}M)`;
            }
        });
        
        // Character counter for name field
        nameInput.addEventListener('input', function() {
            const length = this.value.length;
            const maxLength = 255;
            const remaining = maxLength - length;
            
            let helpText = this.parentNode.querySelector('.form-text');
            if (length > 0) {
                helpText.innerHTML = `Enter a descriptive name for your budget book. ${remaining} characters remaining.`;
                if (remaining < 50) {
                    helpText.className = 'form-text text-warning';
                } else {
                    helpText.className = 'form-text';
                }
            }
        });

        console.log('Create Budget Book form initialized successfully');
    });
</script>
<?= $this->endSection() ?>
