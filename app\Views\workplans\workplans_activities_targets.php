<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .admin-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.12);
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .table th {
        background-color: #f8f9fa;
        border-top: none;
        color: #800000;
        font-weight: 600;
    }

    .btn-action {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        margin: 0 0.125rem;
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .type-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
    }

    .workplan-info {
        background: linear-gradient(135deg, #800000 0%, #28a745 100%);
        color: white;
        border-radius: 10px;
        padding: 1rem;
    }

    .activity-info {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        border-left: 4px solid #28a745;
    }

    .stats-card {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        text-align: center;
        border-left: 4px solid #28a745;
    }

    .progress-bar-custom {
        height: 8px;
        border-radius: 4px;
    }

    .priority-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
        border-radius: 8px;
    }

    .target-value {
        font-weight: bold;
        color: #800000;
    }

    .current-value {
        color: #28a745;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Workplan Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="workplan-info">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5 class="mb-1">
                            <i class="fas fa-clipboard-list me-2"></i><?= esc($workplan['position_name']) ?> - Fiscal Year <?= esc($workplan['fiscal_year']) ?>
                        </h5>
                        <p class="mb-0 opacity-75">
                            Supervisor: <?= esc($workplan['supervisor_name']) ?> | 
                            Current Activities: <?= $workplan['activities_count'] ?>
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities') ?>" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left me-2"></i>Back to Activities
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="activity-info">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h6 class="mb-1">
                            <i class="fas fa-bullseye me-2"></i>Activity Targets: <?= esc($activity['name']) ?>
                        </h6>
                        <p class="mb-0 text-muted">
                            Type: <?= esc($activity['type']) ?> | 
                            Status: <?= esc($activity['status']) ?> | 
                            Duration: <?= date('M d', strtotime($activity['start_date'])) ?> - <?= date('M d, Y', strtotime($activity['end_date'])) ?>
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities/' . $activity['id'] . '/targets/create') ?>" class="btn btn-success btn-sm">
                            <i class="fas fa-plus me-2"></i>Add Target
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="stats-card">
                <i class="fas fa-bullseye fs-2 mb-2 text-primary"></i>
                <h4 class="fw-bold"><?= $stats['total_targets'] ?></h4>
                <small class="text-muted">Total Targets</small>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="stats-card">
                <i class="fas fa-check-circle fs-2 mb-2 text-success"></i>
                <h4 class="fw-bold"><?= $stats['completed_targets'] ?></h4>
                <small class="text-muted">Completed</small>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="stats-card">
                <i class="fas fa-play-circle fs-2 mb-2 text-warning"></i>
                <h4 class="fw-bold"><?= $stats['in_progress_targets'] ?></h4>
                <small class="text-muted">In Progress</small>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="stats-card">
                <i class="fas fa-clock fs-2 mb-2 text-info"></i>
                <h4 class="fw-bold"><?= $stats['pending_targets'] ?></h4>
                <small class="text-muted">Pending</small>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="stats-card">
                <i class="fas fa-percentage fs-2 mb-2 text-danger"></i>
                <h4 class="fw-bold"><?= $stats['completion_rate'] ?>%</h4>
                <small class="text-muted">Completion Rate</small>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="stats-card">
                <i class="fas fa-chart-line fs-2 mb-2 text-secondary"></i>
                <h4 class="fw-bold"><?= $stats['average_progress'] ?>%</h4>
                <small class="text-muted">Avg Progress</small>
            </div>
        </div>
    </div>

    <!-- Targets Table -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="fw-bold mb-0" style="color: #800000;">
                        <i class="fas fa-list me-2"></i>Activity Targets
                    </h5>
                    <div class="d-flex gap-2">
                        <input type="text" class="form-control form-control-sm" placeholder="Search targets..." style="width: 200px;">
                        <button class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Target Name</th>
                                <th>Type</th>
                                <th>Target Value</th>
                                <th>Current Value</th>
                                <th>Progress</th>
                                <th>Target Date</th>
                                <th>Priority</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($targets as $target): ?>
                            <tr>
                                <td>
                                    <div>
                                        <strong><?= esc($target['name']) ?></strong>
                                        <br><small class="text-muted"><?= esc($target['description']) ?></small>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                    $typeClass = '';
                                    switch($target['type']) {
                                        case 'Quantitative': $typeClass = 'bg-primary'; break;
                                        case 'Performance': $typeClass = 'bg-warning'; break;
                                        case 'Deliverable': $typeClass = 'bg-info'; break;
                                        case 'Timeline': $typeClass = 'bg-secondary'; break;
                                        case 'Quality': $typeClass = 'bg-success'; break;
                                        default: $typeClass = 'bg-secondary';
                                    }
                                    ?>
                                    <span class="badge <?= $typeClass ?> type-badge"><?= esc($target['type']) ?></span>
                                </td>
                                <td>
                                    <span class="target-value"><?= esc($target['target_value']) ?></span>
                                    <br><small class="text-muted"><?= esc($target['unit']) ?></small>
                                </td>
                                <td>
                                    <span class="current-value"><?= esc($target['current_value']) ?></span>
                                    <br><small class="text-muted"><?= esc($target['unit']) ?></small>
                                </td>
                                <td>
                                    <div class="progress progress-bar-custom mb-1">
                                        <div class="progress-bar bg-success" style="width: <?= $target['completion_percentage'] ?>%"></div>
                                    </div>
                                    <small class="text-muted"><?= $target['completion_percentage'] ?>%</small>
                                </td>
                                <td>
                                    <?= date('M d, Y', strtotime($target['target_date'])) ?>
                                    <br>
                                    <?php
                                    $daysLeft = ceil((strtotime($target['target_date']) - time()) / (60 * 60 * 24));
                                    if ($daysLeft > 0) {
                                        echo '<small class="text-info">' . $daysLeft . ' days left</small>';
                                    } elseif ($daysLeft == 0) {
                                        echo '<small class="text-warning">Due today</small>';
                                    } else {
                                        echo '<small class="text-danger">' . abs($daysLeft) . ' days overdue</small>';
                                    }
                                    ?>
                                </td>
                                <td>
                                    <?php
                                    $priorityClass = '';
                                    switch($target['priority']) {
                                        case 'High': $priorityClass = 'bg-danger text-white'; break;
                                        case 'Medium': $priorityClass = 'bg-warning text-dark'; break;
                                        case 'Low': $priorityClass = 'bg-success text-white'; break;
                                        default: $priorityClass = 'bg-secondary text-white';
                                    }
                                    ?>
                                    <span class="badge <?= $priorityClass ?> priority-badge"><?= esc($target['priority']) ?></span>
                                </td>
                                <td>
                                    <?php
                                    $statusClass = '';
                                    switch($target['status']) {
                                        case 'Completed': $statusClass = 'bg-success'; break;
                                        case 'In Progress': $statusClass = 'bg-warning'; break;
                                        case 'Pending': $statusClass = 'bg-secondary'; break;
                                        default: $statusClass = 'bg-secondary';
                                    }
                                    ?>
                                    <span class="badge <?= $statusClass ?> status-badge"><?= esc($target['status']) ?></span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities/' . $activity['id'] . '/targets/' . $target['id'] . '/edit') ?>"
                                           class="btn btn-outline-primary btn-action"
                                           title="Edit Target">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button"
                                                class="btn btn-outline-danger btn-action"
                                                title="Delete Target"
                                                onclick="confirmDelete(<?= $target['id'] ?>, '<?= esc($target['name']) ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted">
                        Showing <?= count($targets) ?> of <?= count($targets) ?> targets
                    </div>
                    <nav aria-label="Targets pagination">
                        <ul class="pagination pagination-sm mb-0">
                            <li class="page-item disabled">
                                <span class="page-link">Previous</span>
                            </li>
                            <li class="page-item active">
                                <span class="page-link">1</span>
                            </li>
                            <li class="page-item disabled">
                                <span class="page-link">Next</span>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="d-flex gap-2">
                <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities') ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Activities
                </a>
                <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities/' . $activity['id'] . '/targets/create') ?>" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>Add New Target
                </a>
                <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities/' . $activity['id'] . '/edit') ?>" class="btn btn-outline-primary">
                    <i class="fas fa-edit me-2"></i>Edit Activity
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete target <strong id="deleteTargetName"></strong>?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete Target</button>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    let deleteTargetId = null;

    function confirmDelete(targetId, targetName) {
        deleteTargetId = targetId;
        document.getElementById('deleteTargetName').textContent = targetName;
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        deleteModal.show();
    }

    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
        if (deleteTargetId) {
            // Create a form to submit DELETE request
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `<?= site_url('workplan-management/' . $workplan['id'] . '/activities/' . $activity['id'] . '/targets/') ?>${deleteTargetId}`;
            
            // Add method override for DELETE
            const methodInput = document.createElement('input');
            methodInput.type = 'hidden';
            methodInput.name = '_method';
            methodInput.value = 'DELETE';
            form.appendChild(methodInput);
            
            document.body.appendChild(form);
            form.submit();
        }
    });

    // Search functionality
    document.querySelector('input[placeholder="Search targets..."]').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const tableRows = document.querySelectorAll('tbody tr');

        tableRows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });

    console.log('Activity Targets initialized successfully');
</script>
<?= $this->endSection() ?>
