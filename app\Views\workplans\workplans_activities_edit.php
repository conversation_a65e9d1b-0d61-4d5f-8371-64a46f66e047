<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .admin-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.12);
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .form-label {
        font-weight: 600;
        color: #800000;
    }

    .form-control:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    .form-select:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    .btn-success {
        background-color: #28a745;
        border-color: #28a745;
    }

    .btn-success:hover {
        background-color: #218838;
        border-color: #1e7e34;
    }

    .required {
        color: #dc3545;
    }

    .workplan-info {
        background: linear-gradient(135deg, #800000 0%, #28a745 100%);
        color: white;
        border-radius: 10px;
        padding: 1rem;
    }

    .info-badge {
        background-color: #e9ecef;
        color: #495057;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-size: 0.875rem;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Workplan Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="workplan-info">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5 class="mb-1">
                            <i class="fas fa-clipboard-list me-2"></i><?= esc($workplan['position_name']) ?> - Fiscal Year <?= esc($workplan['fiscal_year']) ?>
                        </h5>
                        <p class="mb-0 opacity-75">
                            Supervisor: <?= esc($workplan['supervisor_name']) ?> | 
                            Current Activities: <?= $workplan['activities_count'] ?>
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="btn-group">
                            <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities') ?>" class="btn btn-outline-light">
                                <i class="fas fa-arrow-left me-2"></i>Back to Activities
                            </a>
                            <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities/' . $activity['id'] . '/link') ?>" class="btn btn-light">
                                <i class="fas fa-link me-2"></i>Link Resources
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-edit me-2"></i>Edit Activity
                        </h3>
                        <p class="text-muted mb-0">
                            Update activity information: <?= esc($activity['name']) ?>
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Activities
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <h6 class="fw-bold text-info mb-3">
                    <i class="fas fa-info-circle me-2"></i>Current Activity Information
                </h6>
                <div class="row g-3">
                    <div class="col-md-3">
                        <div class="info-badge">
                            <strong>Type:</strong><br>
                            <?= esc($activity['type']) ?>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-badge">
                            <strong>Status:</strong><br>
                            <span class="badge bg-<?= $activity['status'] === 'Completed' ? 'success' : ($activity['status'] === 'In Progress' ? 'warning' : 'secondary') ?>"><?= esc($activity['status']) ?></span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-badge">
                            <strong>Duration:</strong><br>
                            <?= date('M d', strtotime($activity['start_date'])) ?> - <?= date('M d, Y', strtotime($activity['end_date'])) ?>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-badge">
                            <strong>Resources:</strong><br>
                            <?= $activity['assets_count'] ?> assets, <?= $activity['officers_count'] ?> officers
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <h5 class="fw-bold mb-4" style="color: #800000;">
                    <i class="fas fa-tasks me-2"></i>Update Activity Information
                </h5>

                <form action="<?= site_url('workplan-management/' . $workplan['id'] . '/activities/' . $activity['id']) ?>" method="POST">
                    <?= csrf_field() ?>
                    <input type="hidden" name="_method" value="PUT">
                    
                    <div class="row g-4">
                        <!-- Activity Name -->
                        <div class="col-md-8">
                            <label for="name" class="form-label">
                                Activity Name <span class="required">*</span>
                            </label>
                            <input type="text" class="form-control" id="name" name="name" required
                                   value="<?= esc($activity['name']) ?>"
                                   placeholder="Enter activity name...">
                            <div class="form-text">Provide a clear and descriptive name for the activity.</div>
                        </div>

                        <!-- Activity Type -->
                        <div class="col-md-4">
                            <label for="type" class="form-label">
                                Activity Type <span class="required">*</span>
                            </label>
                            <select class="form-select" id="type" name="type" required>
                                <option value="">Select Type</option>
                                <?php foreach ($activity_types as $key => $value): ?>
                                    <option value="<?= $key ?>" <?= $key === $activity['type'] ? 'selected' : '' ?>>
                                        <?= esc($value) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Select the type of activity.</div>
                        </div>

                        <!-- Assigned Position -->
                        <div class="col-md-6">
                            <label for="position_id" class="form-label">
                                Assigned Position <span class="required">*</span>
                            </label>
                            <select class="form-select" id="position_id" name="position_id" required>
                                <option value="">Select Position</option>
                                <?php foreach ($positions as $position): ?>
                                    <option value="<?= $position['id'] ?>" 
                                            <?= $position['name'] === $activity['position_name'] ? 'selected' : '' ?>>
                                        <?= esc($position['name']) ?> (<?= esc($position['group']) ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Select the position responsible for this activity.</div>
                        </div>

                        <!-- Priority -->
                        <div class="col-md-6">
                            <label for="priority" class="form-label">
                                Priority <span class="required">*</span>
                            </label>
                            <select class="form-select" id="priority" name="priority" required>
                                <option value="">Select Priority</option>
                                <option value="High">High</option>
                                <option value="Medium" selected>Medium</option>
                                <option value="Low">Low</option>
                            </select>
                            <div class="form-text">Set the priority level for this activity.</div>
                        </div>

                        <!-- Start Date -->
                        <div class="col-md-6">
                            <label for="start_date" class="form-label">
                                Start Date <span class="required">*</span>
                            </label>
                            <input type="date" class="form-control" id="start_date" name="start_date" required
                                   value="<?= esc($activity['start_date']) ?>">
                            <div class="form-text">When should this activity begin?</div>
                        </div>

                        <!-- End Date -->
                        <div class="col-md-6">
                            <label for="end_date" class="form-label">
                                End Date <span class="required">*</span>
                            </label>
                            <input type="date" class="form-control" id="end_date" name="end_date" required
                                   value="<?= esc($activity['end_date']) ?>">
                            <div class="form-text">When should this activity be completed?</div>
                        </div>

                        <!-- Status -->
                        <div class="col-md-6">
                            <label for="status" class="form-label">
                                Status <span class="required">*</span>
                            </label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="Pending" <?= $activity['status'] === 'Pending' ? 'selected' : '' ?>>Pending</option>
                                <option value="In Progress" <?= $activity['status'] === 'In Progress' ? 'selected' : '' ?>>In Progress</option>
                                <option value="Completed" <?= $activity['status'] === 'Completed' ? 'selected' : '' ?>>Completed</option>
                            </select>
                            <div class="form-text">Update the status of the activity.</div>
                        </div>

                        <!-- Expected Outcome -->
                        <div class="col-md-6">
                            <label for="expected_outcome" class="form-label">
                                Expected Outcome
                            </label>
                            <input type="text" class="form-control" id="expected_outcome" name="expected_outcome"
                                   value="Successful completion of <?= esc($activity['name']) ?>"
                                   placeholder="Enter expected outcome...">
                            <div class="form-text">What is the expected result of this activity?</div>
                        </div>

                        <!-- Description -->
                        <div class="col-12">
                            <label for="description" class="form-label">
                                Description <span class="required">*</span>
                            </label>
                            <textarea class="form-control" id="description" name="description" rows="4" required
                                      placeholder="Enter detailed description of the activity..."><?= esc($activity['name']) ?> involves comprehensive planning and execution to achieve the specified objectives within the allocated timeframe. This activity requires coordination with relevant stakeholders and proper resource allocation.</textarea>
                            <div class="form-text">Provide a detailed description of what this activity involves.</div>
                        </div>

                        <!-- Objectives -->
                        <div class="col-12">
                            <label for="objectives" class="form-label">
                                Objectives
                            </label>
                            <textarea class="form-control" id="objectives" name="objectives" rows="3"
                                      placeholder="Enter specific objectives for this activity...">1. Complete all planned tasks within the specified timeframe
2. Ensure quality standards are met
3. Coordinate effectively with team members and stakeholders
4. Document progress and outcomes for future reference</textarea>
                            <div class="form-text">List the specific objectives this activity aims to achieve.</div>
                        </div>

                        <!-- Remarks -->
                        <div class="col-12">
                            <label for="remarks" class="form-label">
                                Remarks
                            </label>
                            <textarea class="form-control" id="remarks" name="remarks" rows="2"
                                      placeholder="Enter any additional remarks or notes...">Activity updated on <?= date('M d, Y') ?>. Current status: <?= esc($activity['status']) ?>. Linked to <?= esc($activity['linked_plan']) ?> and budget code <?= esc($activity['budget_code']) ?>.</textarea>
                            <div class="form-text">Optional remarks or additional notes.</div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-2"></i>Update Activity
                                </button>
                                <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities') ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                                <a href="<?= site_url('workplan-management/' . $workplan['id'] . '/activities/' . $activity['id'] . '/link') ?>" class="btn btn-outline-info">
                                    <i class="fas fa-link me-2"></i>Link Resources
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Form validation
        const form = document.querySelector('form');
        const startDateInput = document.getElementById('start_date');
        const endDateInput = document.getElementById('end_date');

        // Validate end date is after start date
        startDateInput.addEventListener('change', function() {
            endDateInput.min = this.value;
            if (endDateInput.value && endDateInput.value < this.value) {
                endDateInput.value = '';
            }
        });

        endDateInput.addEventListener('change', function() {
            if (startDateInput.value && this.value < startDateInput.value) {
                alert('End date cannot be before start date.');
                this.value = '';
            }
        });

        // Form submission validation
        form.addEventListener('submit', function(e) {
            const startDate = new Date(startDateInput.value);
            const endDate = new Date(endDateInput.value);

            if (endDate <= startDate) {
                e.preventDefault();
                alert('End date must be after start date.');
                return false;
            }

            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';
            submitBtn.disabled = true;

            // Re-enable button after 3 seconds (in case of validation errors)
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 3000);
        });

        console.log('Edit Activity form initialized successfully');
    });
</script>
<?= $this->endSection() ?>
