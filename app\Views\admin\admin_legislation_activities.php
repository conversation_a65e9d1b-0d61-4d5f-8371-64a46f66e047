<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .admin-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.12);
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .table th {
        background-color: #f8f9fa;
        border-top: none;
        color: #800000;
        font-weight: 600;
    }

    .btn-action {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        margin: 0 0.125rem;
    }

    .priority-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .legislation-info {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .activity-card {
        border-left: 4px solid #28a745;
        transition: all 0.3s ease;
    }

    .activity-card:hover {
        border-left-color: #800000;
        transform: translateX(5px);
    }

    .group-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
        margin: 0.1rem;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Legislation Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="legislation-info">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-balance-scale me-2"></i><?= esc($legislation['name']) ?>
                        </h3>
                        <p class="text-muted mb-2"><?= esc($legislation['description']) ?></p>
                        <div class="d-flex gap-3">
                            <span class="badge bg-primary"><?= esc($legislation['reference_number']) ?></span>
                            <span class="badge bg-success"><?= esc($legislation['status']) ?></span>
                            <span class="text-muted">Enacted: <?= date('M d, Y', strtotime($legislation['date_enacted'])) ?></span>
                        </div>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="btn-group">
                            <a href="<?= site_url('admin/legislation') ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Legislation
                            </a>
                            <a href="<?= site_url('admin/legislation/' . $legislation['id'] . '/activities/create') ?>" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>Add Activity
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activities List -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="fw-bold mb-0" style="color: #800000;">
                        <i class="fas fa-tasks me-2"></i>Legislated Activities (<?= count($activities) ?>)
                    </h5>
                    <div class="d-flex gap-2">
                        <input type="text" class="form-control form-control-sm" placeholder="Search activities..." style="width: 200px;">
                        <button class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <?php if (empty($activities)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Activities Defined</h5>
                        <p class="text-muted mb-3">This legislation doesn't have any activities defined yet.</p>
                        <a href="<?= site_url('admin/legislation/' . $legislation['id'] . '/activities/create') ?>" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>Add First Activity
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Activity Name</th>
                                    <th>Priority</th>
                                    <th>Frequency</th>
                                    <th>Due Period</th>
                                    <th>Linked Groups</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($activities as $activity): ?>
                                <tr class="activity-card">
                                    <td>
                                        <div>
                                            <strong><?= esc($activity['name']) ?></strong>
                                            <br><small class="text-muted"><?= esc($activity['description']) ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge priority-badge bg-<?= $activity['priority'] === 'Critical' ? 'danger' : ($activity['priority'] === 'High' ? 'warning' : 'info') ?>">
                                            <?= esc($activity['priority']) ?>
                                        </span>
                                    </td>
                                    <td><?= esc($activity['frequency']) ?></td>
                                    <td>
                                        <span class="text-primary fw-bold"><?= esc($activity['due_period']) ?></span>
                                    </td>
                                    <td>
                                        <?php 
                                        $linkedGroups = [];
                                        foreach ($groups as $group) {
                                            if (in_array($group['id'], $activity['linked_groups'])) {
                                                $linkedGroups[] = $group['name'];
                                            }
                                        }
                                        ?>
                                        <?php foreach ($linkedGroups as $groupName): ?>
                                            <span class="badge bg-secondary group-badge"><?= esc($groupName) ?></span>
                                        <?php endforeach; ?>
                                        <?php if (empty($linkedGroups)): ?>
                                            <span class="text-muted">No groups linked</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= site_url('admin/legislation/' . $legislation['id'] . '/activities/' . $activity['id'] . '/edit') ?>"
                                               class="btn btn-outline-secondary btn-action"
                                               title="Edit Activity">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form method="post" action="<?= site_url('admin/legislation/' . $legislation['id'] . '/activities/' . $activity['id']) ?>" style="display: inline;">
                                                <?= csrf_field() ?>
                                                <input type="hidden" name="_method" value="DELETE">
                                                <button type="submit"
                                                        class="btn btn-outline-danger btn-action"
                                                        title="Delete Activity"
                                                        onclick="return confirm('Are you sure you want to delete this activity?')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <h5 class="fw-bold mb-3" style="color: #800000;">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h5>
                <div class="row g-3">
                    <div class="col-lg-3 col-md-6">
                        <a href="<?= site_url('admin/legislation/' . $legislation['id'] . '/activities/create') ?>" class="btn btn-outline-primary w-100 py-3">
                            <i class="fas fa-plus-circle me-2"></i>Add Activity
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <a href="<?= site_url('admin/legislation/' . $legislation['id'] . '/edit') ?>" class="btn btn-outline-secondary w-100 py-3">
                            <i class="fas fa-edit me-2"></i>Edit Legislation
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <button class="btn btn-outline-info w-100 py-3">
                            <i class="fas fa-file-export me-2"></i>Export Activities
                        </button>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <button class="btn btn-outline-warning w-100 py-3">
                            <i class="fas fa-chart-bar me-2"></i>View Reports
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Search functionality
        document.querySelector('input[placeholder="Search activities..."]').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const tableRows = document.querySelectorAll('tbody tr');

            tableRows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });

        console.log('Legislation Activities page initialized successfully');
    });
</script>
<?= $this->endSection() ?>
