<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Home::index');
$routes->get('home', 'Home::index');
$routes->get('home/login', 'Home::login');
$routes->post('home/authenticate', 'Home::authenticate');
$routes->get('dashboard', 'DashboardController::index');

// Dakoii System Routes
$routes->get('dakoii', 'Dakoii::index');
$routes->get('dakoii/dashboard', 'Dakoii::dashboard');
$routes->post('dakoii/authenticate', 'Dakoii::authenticate');
$routes->get('dakoii/organizations', 'Dakoii::organizations');
$routes->get('dakoii/organizations/create', 'Dakoii::createOrganization');
$routes->get('dakoii/admins', 'Dakoii::admins');
$routes->get('dakoii/admins/create', 'Dakoii::createAdmin');
$routes->get('dakoii/settings', 'Dakoii::settings');
$routes->get('dakoii/reports', 'Dakoii::reports');

// Administrator Settings Routes
$routes->group('admin', function($routes) {
    // Main admin dashboard
    $routes->get('/', 'AdminController::index');

    // Users Management Routes
    $routes->get('users', 'AdminController::users');
    $routes->get('users/create', 'AdminController::createUser');
    $routes->post('users', 'AdminController::storeUser');
    $routes->get('users/(:num)/edit', 'AdminController::editUser/$1');
    $routes->put('users/(:num)', 'AdminController::updateUser/$1');
    $routes->delete('users/(:num)', 'AdminController::deleteUser/$1');

    // Structure Management Routes - Using AdminStructureController
    $routes->get('structures', 'AdminStructureController::index');
    $routes->get('structures/create', 'AdminStructureController::create');
    $routes->post('structures', 'AdminStructureController::store');
    $routes->get('structures/(:num)/edit', 'AdminStructureController::edit/$1');
    $routes->put('structures/(:num)', 'AdminStructureController::update/$1');
    $routes->post('structures/(:num)/activate', 'AdminStructureController::activate/$1');
    $routes->delete('structures/(:num)', 'AdminStructureController::delete/$1');

    // Group Management Routes
    $routes->get('structures/(:num)/groups', 'AdminStructureController::groups/$1');
    $routes->get('structures/(:num)/groups/create', 'AdminStructureController::createGroup/$1');
    $routes->post('structures/(:num)/groups', 'AdminStructureController::storeGroup/$1');
    $routes->get('structures/(:num)/groups/(:num)/edit', 'AdminStructureController::editGroup/$1/$2');
    $routes->put('structures/(:num)/groups/(:num)', 'AdminStructureController::updateGroup/$1/$2');
    $routes->delete('structures/(:num)/groups/(:num)', 'AdminStructureController::deleteGroup/$1/$2');

    // Position Management Routes
    $routes->get('groups/(:num)/positions', 'AdminStructureController::positions/$1');
    $routes->get('groups/(:num)/positions/create', 'AdminStructureController::createPosition/$1');
    $routes->post('groups/(:num)/positions', 'AdminStructureController::storePosition/$1');
    $routes->get('groups/(:num)/positions/(:num)/edit', 'AdminStructureController::editPosition/$1/$2');
    $routes->put('groups/(:num)/positions/(:num)', 'AdminStructureController::updatePosition/$1/$2');
    $routes->delete('groups/(:num)/positions/(:num)', 'AdminStructureController::deletePosition/$1/$2');

    // Plans Management Routes - Using AdminPlansController
    $routes->get('plans', 'AdminPlansController::index');
    $routes->get('plans/create', 'AdminPlansController::create');
    $routes->post('plans', 'AdminPlansController::store');
    $routes->get('plans/(:num)/edit', 'AdminPlansController::edit/$1');
    $routes->put('plans/(:num)', 'AdminPlansController::update/$1');
    $routes->delete('plans/(:num)', 'AdminPlansController::delete/$1');

    // Development Plans - Programs Management Routes
    $routes->get('plans/(:num)/programs', 'AdminPlansController::programs/$1');
    $routes->get('plans/(:num)/programs/create', 'AdminPlansController::createProgram/$1');
    $routes->post('plans/(:num)/programs', 'AdminPlansController::storeProgram/$1');

    // Corporate Plans - KRAs Management Routes
    $routes->get('plans/(:num)/kras', 'AdminPlansController::kras/$1');
    $routes->get('plans/(:num)/kras/create', 'AdminPlansController::createKRA/$1');
    $routes->post('plans/(:num)/kras', 'AdminPlansController::storeKRA/$1');

    // Projects Management Routes (under Programs)
    $routes->get('programs/(:num)/projects', 'AdminPlansController::projects/$1');
    $routes->get('programs/(:num)/projects/create', 'AdminPlansController::createProject/$1');
    $routes->post('programs/(:num)/projects', 'AdminPlansController::storeProject/$1');

    // KPIs Management Routes (under KRAs)
    $routes->get('kras/(:num)/kpis', 'AdminPlansController::kpis/$1');
    $routes->get('kras/(:num)/kpis/create', 'AdminPlansController::createKPI/$1');
    $routes->post('kras/(:num)/kpis', 'AdminPlansController::storeKPI/$1');

    // Budget Books Management Routes - Using AdminBudgetController
    $routes->get('budget-books', 'AdminBudgetController::index');
    $routes->get('budget-books/create', 'AdminBudgetController::create');
    $routes->post('budget-books', 'AdminBudgetController::store');
    $routes->get('budget-books/(:num)/edit', 'AdminBudgetController::edit/$1');
    $routes->put('budget-books/(:num)', 'AdminBudgetController::update/$1');
    $routes->delete('budget-books/(:num)', 'AdminBudgetController::delete/$1');

    // Budget Codes Management Routes (under Budget Books)
    $routes->get('budget-books/(:num)/codes', 'AdminBudgetController::budgetCodes/$1');
    $routes->get('budget-books/(:num)/codes/create', 'AdminBudgetController::createBudgetCode/$1');
    $routes->post('budget-books/(:num)/codes', 'AdminBudgetController::storeBudgetCode/$1');
    $routes->get('budget-books/(:num)/codes/(:num)/edit', 'AdminBudgetController::editBudgetCode/$1/$2');
    $routes->put('budget-books/(:num)/codes/(:num)', 'AdminBudgetController::updateBudgetCode/$1/$2');
    $routes->delete('budget-books/(:num)/codes/(:num)', 'AdminBudgetController::deleteBudgetCode/$1/$2');

    // Appointments Routes
    $routes->get('appointments', 'AdminController::appointments');
    $routes->get('appointments/create', 'AdminController::createAppointment');
    $routes->post('appointments', 'AdminController::storeAppointment');

    // Plans Management Routes
    $routes->get('plans', 'AdminController::plans');

    // Budget Book Management Routes
    $routes->get('budget-books', 'AdminController::budgetBooks');

    // Legislated Activities Routes - Using AdminLegislationController
    $routes->get('legislated-activities', 'AdminController::legislatedActivities');

    // Legislation Management Routes
    $routes->get('legislation', 'AdminLegislationController::index');
    $routes->get('legislation/create', 'AdminLegislationController::create');
    $routes->post('legislation', 'AdminLegislationController::store');
    $routes->get('legislation/(:num)/edit', 'AdminLegislationController::edit/$1');
    $routes->put('legislation/(:num)', 'AdminLegislationController::update/$1');
    $routes->delete('legislation/(:num)', 'AdminLegislationController::delete/$1');
});

// Workplan Management Routes
$routes->group('workplan-management', function($routes) {
    // Main workplan routes
    $routes->get('/', 'WorkplanController::index');
    $routes->get('create', 'WorkplanController::create');
    $routes->post('/', 'WorkplanController::store');
    $routes->get('(:num)/edit', 'WorkplanController::edit/$1');
    $routes->put('(:num)', 'WorkplanController::update/$1');
    $routes->delete('(:num)', 'WorkplanController::delete/$1');

    // Activities management routes
    $routes->get('(:num)/activities', 'WorkplanController::activities/$1');
    $routes->get('(:num)/activities/create', 'WorkplanController::createActivity/$1');
    $routes->post('(:num)/activities', 'WorkplanController::storeActivity/$1');
    $routes->get('(:num)/activities/(:num)/edit', 'WorkplanController::editActivity/$1/$2');
    $routes->put('(:num)/activities/(:num)', 'WorkplanController::updateActivity/$1/$2');
    $routes->delete('(:num)/activities/(:num)', 'WorkplanController::deleteActivity/$1/$2');

    // Activity linking routes
    $routes->get('(:num)/activities/(:num)/link', 'WorkplanController::linkActivity/$1/$2');
    $routes->post('(:num)/activities/(:num)/link', 'WorkplanController::storeLinkActivity/$1/$2');

    // Activity targets management routes
    $routes->get('(:num)/activities/(:num)/targets', 'WorkplanController::targets/$1/$2');
    $routes->get('(:num)/activities/(:num)/targets/create', 'WorkplanController::createTarget/$1/$2');
    $routes->post('(:num)/activities/(:num)/targets', 'WorkplanController::storeTarget/$1/$2');
    $routes->get('(:num)/activities/(:num)/targets/(:num)/edit', 'WorkplanController::editTarget/$1/$2/$3');
    $routes->put('(:num)/activities/(:num)/targets/(:num)', 'WorkplanController::updateTarget/$1/$2/$3');
    $routes->delete('(:num)/activities/(:num)/targets/(:num)', 'WorkplanController::deleteTarget/$1/$2/$3');

    // Activities Management Routes (under Legislation)
    $routes->get('legislation/(:num)/activities', 'AdminLegislationController::activities/$1');
    $routes->get('legislation/(:num)/activities/create', 'AdminLegislationController::createActivity/$1');
    $routes->post('legislation/(:num)/activities', 'AdminLegislationController::storeActivity/$1');
    $routes->get('legislation/(:num)/activities/(:num)/edit', 'AdminLegislationController::editActivity/$1/$2');
    $routes->put('legislation/(:num)/activities/(:num)', 'AdminLegislationController::updateActivity/$1/$2');
    $routes->delete('legislation/(:num)/activities/(:num)', 'AdminLegislationController::deleteActivity/$1/$2');
});
